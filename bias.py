import spacy
from transformers import pipeline
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import nltk
from nltk.tokenize import sent_tokenize
from typing import Dict, List, Tuple
import re

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

class LegalAnalyzer:
    def __init__(self):
        # Load spaCy model for legal text
        self.nlp = spacy.load("en_core_web_sm")
        
        # Initialize transformers pipeline for text generation (CPU only)
        self.summarizer = pipeline("summarization", 
                                 model="facebook/bart-large-cnn",
                                 device=-1)  # Force CPU usage
        
        # Initialize sentiment analysis (CPU only)
        self.sentiment_analyzer = pipeline("sentiment-analysis",
                                         device=-1)  # Force CPU usage
        
        # Legal-specific bias keywords
        self.bias_keywords = {
            'gender_bias': ['he', 'she', 'man', 'woman', 'male', 'female', 'gender'],
            'racial_bias': ['race', 'ethnicity', 'color', 'black', 'white', 'asian', 'hispanic'],
            'age_bias': ['young', 'old', 'elderly', 'youth', 'senior', 'junior'],
            'economic_bias': ['rich', 'poor', 'wealthy', 'poverty', 'affluent', 'underprivileged'],
            'religious_bias': ['religion', 'faith', 'christian', 'muslim', 'jewish', 'hindu', 'buddhist'],
            'political_bias': ['liberal', 'conservative', 'democrat', 'republican', 'left-wing', 'right-wing'],
            'educational_bias': ['educated', 'uneducated', 'degree', 'academic', 'scholar'],
            'occupational_bias': ['professional', 'worker', 'laborer', 'executive', 'manager']
        }
        
        # Legal-specific terms for key point extraction
        self.legal_keywords = [
            'therefore', 'conclude', 'hold', 'rule', 'find', 'determine',
            'judgment', 'verdict', 'decision', 'opinion', 'conclusion',
            'plaintiff', 'defendant', 'court', 'judge', 'jury', 'evidence',
            'testimony', 'witness', 'expert', 'exhibit', 'motion', 'appeal'
        ]

    def analyze_document(self, text: str) -> Dict:
        """Perform comprehensive analysis of legal document."""
        doc = self.nlp(text)
        
        # Generate summary
        summary = self.generate_summary(text)
        
        # Extract key points
        key_points = self.extract_key_points(text)
        
        # Detect biases
        bias_analysis = self.detect_bias(text)
        
        # Analyze sentiment
        sentiment = self.analyze_sentiment(text)
        
        # Extract legal entities
        entities = self.extract_legal_entities(doc)
        
        # Analyze document structure
        structure = self.analyze_structure(doc)
        
        return {
            'summary': summary,
            'key_points': key_points,
            'bias_analysis': bias_analysis,
            'sentiment': sentiment,
            'entities': entities,
            'structure': structure
        }

    def generate_summary(self, text: str) -> str:
        """Generate a concise summary using BART model."""
        try:
            summary = self.summarizer(text, max_length=130, min_length=30, do_sample=False)
            return summary[0]['summary_text']
        except Exception as e:
            # Fallback to rule-based summarization
            sentences = sent_tokenize(text)
            summary = []
            for sentence in sentences:
                if len(sentence.split()) > 10 and any(keyword in sentence.lower() for keyword in self.legal_keywords):
                    summary.append(sentence)
            return ' '.join(summary[:3])

    def extract_key_points(self, text: str) -> List[str]:
        """Extract key legal points using both rule-based and ML approaches."""
        sentences = sent_tokenize(text)
        key_points = []
        
        # Rule-based extraction
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in self.legal_keywords):
                key_points.append(sentence)
        
        # ML-based extraction (using sentence embeddings)
        if len(sentences) > 5:
            vectorizer = TfidfVectorizer()
            try:
                tfidf_matrix = vectorizer.fit_transform(sentences)
                cosine_similarities = cosine_similarity(tfidf_matrix)
                sentence_scores = cosine_similarities.sum(axis=1)
                top_indices = sentence_scores.argsort()[-3:][::-1]
                for idx in top_indices:
                    if sentences[idx] not in key_points:
                        key_points.append(sentences[idx])
            except Exception:
                pass
        
        return key_points[:5]

    def detect_bias(self, text: str) -> Dict[str, List[str]]:
        """Detect various types of bias in the text."""
        text = text.lower()
        bias_results = {}
        
        for bias_type, keywords in self.bias_keywords.items():
            matches = []
            for keyword in keywords:
                if keyword in text:
                    matches.append(keyword)
            if matches:
                bias_results[bias_type] = matches
        
        return bias_results

    def analyze_sentiment(self, text: str) -> Dict:
        """Analyze the sentiment of the document."""
        try:
            # Split into chunks if text is too long
            chunks = [text[i:i+512] for i in range(0, len(text), 512)]
            sentiments = []
            
            for chunk in chunks:
                result = self.sentiment_analyzer(chunk)[0]
                sentiments.append(result)
            
            # Aggregate sentiments
            avg_score = np.mean([s['score'] for s in sentiments])
            overall_sentiment = 'positive' if avg_score > 0.5 else 'negative'
            
            return {
                'overall_sentiment': overall_sentiment,
                'confidence': avg_score,
                'details': sentiments
            }
        except Exception:
            return {'overall_sentiment': 'neutral', 'confidence': 0.5, 'details': []}

    def extract_legal_entities(self, doc) -> Dict[str, List[str]]:
        """Extract legal entities from the document."""
        entities = {
            'organizations': [],
            'persons': [],
            'dates': [],
            'locations': [],
            'laws': []
        }
        
        for ent in doc.ents:
            if ent.label_ == 'ORG':
                entities['organizations'].append(ent.text)
            elif ent.label_ == 'PERSON':
                entities['persons'].append(ent.text)
            elif ent.label_ == 'DATE':
                entities['dates'].append(ent.text)
            elif ent.label_ == 'GPE':
                entities['locations'].append(ent.text)
            elif ent.label_ == 'LAW':
                entities['laws'].append(ent.text)
        
        return entities

    def analyze_structure(self, doc) -> Dict:
        """Analyze the document structure and formatting."""
        paragraphs = [p.text for p in doc.sents]
        
        return {
            'total_paragraphs': len(paragraphs),
            'avg_paragraph_length': np.mean([len(p.split()) for p in paragraphs]),
            'total_words': len(doc),
            'total_sentences': len(list(doc.sents)),
            'has_headings': any(p.is_title() for p in doc),
            'has_citations': bool(re.search(r'\d+\.\d+', doc.text)),
            'has_legal_references': bool(re.search(r'§|article|section|clause', doc.text.lower()))
        }

    def find_similar_cases(self, text: str, past_cases: List[Dict]) -> List[Dict]:
        """Find similar past cases using advanced similarity matching."""
        if not past_cases:
            return []
        
        # Prepare text for comparison
        vectorizer = TfidfVectorizer()
        case_texts = [f"{case['title']} {case['summary']}" for case in past_cases]
        case_texts.append(text)
        
        try:
            # Create TF-IDF matrix
            tfidf_matrix = vectorizer.fit_transform(case_texts)
            
            # Calculate cosine similarities
            cosine_similarities = cosine_similarity(tfidf_matrix)
            
            # Get similarities with the input text
            text_similarities = cosine_similarities[-1][:-1]
            
            # Get top similar cases
            top_indices = text_similarities.argsort()[-3:][::-1]
            similar_cases = [past_cases[i] for i in top_indices if text_similarities[i] > 0.1]
            
            return similar_cases
        except Exception:
            # Fallback to simple keyword matching
            text = text.lower()
            similar_cases = []
            
            for case in past_cases:
                case_text = f"{case['title']} {case['summary']}".lower()
                common_keywords = set(text.split()) & set(case_text.split())
                if len(common_keywords) > 5:
                    similar_cases.append(case)
            
            return similar_cases[:3] 