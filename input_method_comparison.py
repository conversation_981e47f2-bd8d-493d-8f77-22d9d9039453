"""
Comparison test: Diarized Text vs Summarized Text for Bias Detection
Analyzes which input method provides better bias detection results
"""

from courtroom_bias_detector import HybridIndianCourtroomBiasDetector, integrate_bias_detection
import json
import time

def create_test_scenario():
    """Create a comprehensive test scenario with both formats"""
    
    # Original diarized text (full conversation)
    diarized_text = [
        {
            'speaker': 'Judge',
            'text': 'The court is now in session. We are hearing the case of theft against Mr. <PERSON><PERSON>.',
            'timestamp': '00:01:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Your honor, the defendant comes from a scheduled caste background. These people typically engage in such criminal activities due to their inherent nature.',
            'timestamp': '00:02:30'
        },
        {
            'speaker': 'Defense_Lawyer',
            'text': 'Objection, your honor! This is completely inappropriate. My client should be judged based on evidence, not his caste or background.',
            'timestamp': '00:03:15'
        },
        {
            'speaker': 'Judge',
            'text': 'The objection is sustained. However, I must note that the female witness, Mrs. <PERSON>, seemed quite emotional during her testimony. Women are generally more emotional and less reliable in such serious matters.',
            'timestamp': '00:04:00'
        },
        {
            'speaker': 'Witness_Female',
            'text': 'Your honor, I clearly saw the incident. The accused was actually helping an elderly person who had fallen. He seemed like a decent, well-mannered person.',
            'timestamp': '00:05:30'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Your honor, this Muslim defendant clearly shows the typical deceptive behavior of his community. They are known for such tactics - appearing helpful while planning crimes.',
            'timestamp': '00:06:45'
        },
        {
            'speaker': 'Defense_Lawyer',
            'text': 'This is outrageous! Such biased and discriminatory language has no place in a court of law. I demand this be struck from the record.',
            'timestamp': '00:07:30'
        },
        {
            'speaker': 'Judge',
            'text': 'The prosecutor will refrain from such comments. However, it is worth noting that people from poor economic backgrounds often resort to crime due to their circumstances and mentality.',
            'timestamp': '00:08:15'
        },
        {
            'speaker': 'Witness_Male',
            'text': 'I was also present during the incident. The accused person was polite and was genuinely helping the elderly man. There was no suspicious behavior.',
            'timestamp': '00:09:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'These Bihari migrants who come to our city are typical troublemakers. They cannot even speak proper English, so how can we trust their testimony?',
            'timestamp': '00:10:15'
        }
    ]
    
    # Summarized version (what your summarization model might produce)
    summarized_text = [
        {
            'speaker': 'Judge',
            'text': 'Court session opened for theft case against Mr. Rajesh Kumar. Noted concerns about witness reliability.',
            'timestamp': '00:01:00-00:04:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Argued defendant has criminal tendencies based on background and community behavior patterns.',
            'timestamp': '00:02:30-00:10:15'
        },
        {
            'speaker': 'Defense_Lawyer',
            'text': 'Objected to inappropriate comments. Demanded evidence-based judgment and removal of biased statements.',
            'timestamp': '00:03:15-00:07:30'
        },
        {
            'speaker': 'Witness_Female',
            'text': 'Testified that accused was helping elderly person and appeared decent.',
            'timestamp': '00:05:30'
        },
        {
            'speaker': 'Witness_Male',
            'text': 'Confirmed accused was polite and genuinely helping, no suspicious behavior observed.',
            'timestamp': '00:09:00'
        }
    ]
    
    return diarized_text, summarized_text

def analyze_bias_detection_coverage(diarized_results, summarized_results):
    """Analyze and compare bias detection coverage"""
    
    print("\n📊 BIAS DETECTION COVERAGE ANALYSIS")
    print("="*60)
    
    # Extract bias information
    diarized_detections = diarized_results['bias_results']['detections']
    summarized_detections = summarized_results['bias_results']['detections']

    diarized_bias_types = set(d.bias_type for d in diarized_detections)
    summarized_bias_types = set(d.bias_type for d in summarized_detections)
    
    print(f"\n🔍 DIARIZED TEXT RESULTS:")
    print(f"   Total bias instances: {len(diarized_detections)}")
    print(f"   Unique bias types: {len(diarized_bias_types)}")
    print(f"   Bias types found: {', '.join(diarized_bias_types)}")
    print(f"   Average confidence: {diarized_results['confidence_metrics']['average_confidence']:.2f}")
    
    print(f"\n📝 SUMMARIZED TEXT RESULTS:")
    print(f"   Total bias instances: {len(summarized_detections)}")
    print(f"   Unique bias types: {len(summarized_bias_types)}")
    print(f"   Bias types found: {', '.join(summarized_bias_types) if summarized_bias_types else 'None'}")
    print(f"   Average confidence: {summarized_results['confidence_metrics']['average_confidence']:.2f}")
    
    # Calculate coverage metrics
    missed_in_summary = diarized_bias_types - summarized_bias_types
    unique_to_summary = summarized_bias_types - diarized_bias_types
    
    print(f"\n📈 COVERAGE COMPARISON:")
    print(f"   Bias types missed in summary: {len(missed_in_summary)}")
    if missed_in_summary:
        print(f"   Missed types: {', '.join(missed_in_summary)}")
    
    print(f"   Bias types unique to summary: {len(unique_to_summary)}")
    if unique_to_summary:
        print(f"   Unique types: {', '.join(unique_to_summary)}")
    
    coverage_percentage = (len(summarized_bias_types) / len(diarized_bias_types) * 100) if diarized_bias_types else 0
    print(f"   Summary coverage: {coverage_percentage:.1f}%")
    
    return {
        'diarized_count': len(diarized_detections),
        'summarized_count': len(summarized_detections),
        'coverage_percentage': coverage_percentage,
        'missed_types': list(missed_in_summary),
        'unique_to_summary': list(unique_to_summary)
    }

def analyze_specific_bias_instances(diarized_results, summarized_results):
    """Analyze specific bias instances that might be lost"""
    
    print("\n🔍 DETAILED BIAS INSTANCE ANALYSIS")
    print("="*60)
    
    diarized_detections = diarized_results['bias_results']['detections']
    
    print("\n📋 BIAS INSTANCES IN ORIGINAL DIARIZED TEXT:")
    print("-" * 50)
    
    for i, detection in enumerate(diarized_detections, 1):
        print(f"\n{i}. {detection.speaker} ({detection.timestamp}):")
        print(f"   Bias Type: {detection.bias_type}")
        print(f"   Severity: {detection.severity}")
        print(f"   Confidence: {detection.confidence_score:.2f}")
        print(f"   Statement: '{detection.instances[0][:100]}...'")
        print(f"   Markers: {', '.join(detection.linguistic_markers[:3])}")
    
    summarized_detections = summarized_results['bias_results']['detections']
    
    print(f"\n📋 BIAS INSTANCES IN SUMMARIZED TEXT:")
    print("-" * 50)
    
    if summarized_detections:
        for i, detection in enumerate(summarized_detections, 1):
            print(f"\n{i}. {detection.speaker} ({detection.timestamp}):")
            print(f"   Bias Type: {detection.bias_type}")
            print(f"   Severity: {detection.severity}")
            print(f"   Confidence: {detection.confidence_score:.2f}")
            print(f"   Statement: '{detection.instances[0]}'")
    else:
        print("\n   ❌ NO BIAS DETECTED IN SUMMARIZED TEXT")

def analyze_processing_performance(diarized_text, summarized_text):
    """Compare processing performance"""
    
    print("\n⚡ PROCESSING PERFORMANCE COMPARISON")
    print("="*60)
    
    # Test processing time for diarized text
    start_time = time.time()
    diarized_results = integrate_bias_detection(diarized_text, enable_ml=False)
    diarized_time = (time.time() - start_time) * 1000
    
    # Test processing time for summarized text
    start_time = time.time()
    summarized_results = integrate_bias_detection(summarized_text, enable_ml=False)
    summarized_time = (time.time() - start_time) * 1000
    
    # Calculate text statistics
    diarized_chars = sum(len(item['text']) for item in diarized_text)
    summarized_chars = sum(len(item['text']) for item in summarized_text)
    
    print(f"\n📊 PROCESSING METRICS:")
    print(f"   Diarized text:")
    print(f"     Characters: {diarized_chars}")
    print(f"     Processing time: {diarized_time:.2f}ms")
    print(f"     Speed: {diarized_chars/diarized_time*1000:.0f} chars/sec")
    
    print(f"   Summarized text:")
    print(f"     Characters: {summarized_chars}")
    print(f"     Processing time: {summarized_time:.2f}ms")
    print(f"     Speed: {summarized_chars/summarized_time*1000:.0f} chars/sec")
    
    print(f"\n📈 EFFICIENCY COMPARISON:")
    print(f"   Text reduction: {(1 - summarized_chars/diarized_chars)*100:.1f}%")
    print(f"   Speed improvement: {diarized_time/summarized_time:.1f}x faster")
    
    return diarized_results, summarized_results

def generate_recommendation(coverage_analysis):
    """Generate recommendation based on analysis"""
    
    print("\n🎯 RECOMMENDATION ANALYSIS")
    print("="*60)
    
    print(f"\n📋 KEY FINDINGS:")
    print(f"   • Diarized text detected: {coverage_analysis['diarized_count']} bias instances")
    print(f"   • Summarized text detected: {coverage_analysis['summarized_count']} bias instances")
    print(f"   • Coverage by summary: {coverage_analysis['coverage_percentage']:.1f}%")
    
    if coverage_analysis['missed_types']:
        print(f"   • Bias types lost in summary: {', '.join(coverage_analysis['missed_types'])}")
    
    print(f"\n🔍 ANALYSIS:")
    
    if coverage_analysis['coverage_percentage'] < 50:
        recommendation = "DIARIZED TEXT"
        reason = "Summarization loses significant bias information"
        confidence = "HIGH"
    elif coverage_analysis['coverage_percentage'] < 80:
        recommendation = "DIARIZED TEXT (with optional summary analysis)"
        reason = "Moderate bias loss in summarization"
        confidence = "MEDIUM"
    else:
        recommendation = "EITHER (based on performance needs)"
        reason = "Good bias preservation in summarization"
        confidence = "LOW"
    
    print(f"   • Bias information loss: {'HIGH' if coverage_analysis['coverage_percentage'] < 70 else 'MEDIUM' if coverage_analysis['coverage_percentage'] < 90 else 'LOW'}")
    print(f"   • Context preservation: {'POOR' if coverage_analysis['coverage_percentage'] < 60 else 'GOOD'}")
    print(f"   • Detection accuracy impact: {'SIGNIFICANT' if coverage_analysis['coverage_percentage'] < 70 else 'MODERATE'}")
    
    print(f"\n🎯 RECOMMENDATION: {recommendation}")
    print(f"   Reason: {reason}")
    print(f"   Confidence: {confidence}")
    
    return recommendation

def main():
    """Main comparison function"""
    
    print("🔬 INPUT METHOD COMPARISON: DIARIZED vs SUMMARIZED TEXT")
    print("="*70)
    print("Analyzing which input method provides better bias detection for")
    print("Indian courtroom proceedings...")
    print("="*70)
    
    # Create test data
    diarized_text, summarized_text = create_test_scenario()
    
    print(f"\n📄 TEST SCENARIO:")
    print(f"   Diarized segments: {len(diarized_text)}")
    print(f"   Summarized segments: {len(summarized_text)}")
    print(f"   Total characters (diarized): {sum(len(item['text']) for item in diarized_text)}")
    print(f"   Total characters (summarized): {sum(len(item['text']) for item in summarized_text)}")
    
    # Run performance comparison
    diarized_results, summarized_results = analyze_processing_performance(diarized_text, summarized_text)
    
    # Analyze bias detection coverage
    coverage_analysis = analyze_bias_detection_coverage(diarized_results, summarized_results)
    
    # Analyze specific instances
    analyze_specific_bias_instances(diarized_results, summarized_results)
    
    # Generate recommendation
    recommendation = generate_recommendation(coverage_analysis)
    
    # Save detailed results
    comparison_results = {
        'test_scenario': {
            'diarized_segments': len(diarized_text),
            'summarized_segments': len(summarized_text),
            'diarized_chars': sum(len(item['text']) for item in diarized_text),
            'summarized_chars': sum(len(item['text']) for item in summarized_text)
        },
        'bias_detection_results': {
            'diarized': diarized_results,
            'summarized': summarized_results
        },
        'coverage_analysis': coverage_analysis,
        'recommendation': recommendation
    }
    
    # Save results (with serialization handling)
    try:
        with open('input_method_comparison_results.json', 'w', encoding='utf-8') as f:
            # Convert BiasDetection objects to dicts for JSON serialization
            serializable_results = {
                'test_scenario': comparison_results['test_scenario'],
                'coverage_analysis': comparison_results['coverage_analysis'],
                'recommendation': comparison_results['recommendation'],
                'summary': {
                    'diarized_bias_count': len(diarized_results['bias_results']['detections']),
                    'summarized_bias_count': len(summarized_results['bias_results']['detections']),
                    'coverage_percentage': coverage_analysis['coverage_percentage']
                }
            }
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Detailed results saved to: input_method_comparison_results.json")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")
    
    print(f"\n🎉 COMPARISON COMPLETE!")
    print("="*70)

if __name__ == "__main__":
    main()
