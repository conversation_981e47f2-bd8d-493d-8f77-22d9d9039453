"""
Advanced Bias Detection System - Next Iteration
Addressing: Contextual Ambiguity, False Positives, Advanced Semantic Analysis
Target: 90%+ detection rate with <5% false positive rate
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass
from collections import defaultdict, Counter
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
import warnings
warnings.filterwarnings('ignore')

@dataclass
class AdvancedBiasDetection:
    bias_type: str
    severity: str
    confidence_score: float
    detection_method: str
    context_analysis: Dict
    uncertainty_score: float
    explanation: str
    legal_context_score: float  # NEW: Legal legitimacy score
    semantic_confidence: float  # NEW: Semantic analysis confidence
    neutrality_score: float     # NEW: How likely this is neutral/legitimate
    cultural_context: str = ""
    speaker: str = None
    timestamp: str = None
    evidence_text: str = ""
    alternative_interpretations: List[str] = None
    recommendation: str = ""    # NEW: Action recommendation

    def __post_init__(self):
        if self.alternative_interpretations is None:
            self.alternative_interpretations = []

class AdvancedBiasDetector:
    def __init__(self):
        # Initialize NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
            nltk.data.find('corpora/stopwords')
        except LookupError:
            nltk.download('punkt')
            nltk.download('stopwords')
        
        self.stop_words = set(stopwords.words('english'))
        
        # ADVANCED LEGAL CONTEXT UNDERSTANDING
        self.legal_context_database = {
            'legitimate_legal_contexts': {
                'factual_statements': [
                    r'criminal history includes',
                    r'prior convictions for',
                    r'evidence shows that',
                    r'testimony indicates',
                    r'court records show',
                    r'legal procedures require',
                    r'according to the law',
                    r'statutory requirements',
                    r'case precedent',
                    r'legal standards dictate'
                ],
                'procedural_language': [
                    r'community standards (?:require|dictate|mandate)',
                    r'legal (?:consideration|requirement|obligation)',
                    r'court (?:procedure|protocol|standard)',
                    r'judicial (?:review|assessment|evaluation)',
                    r'sentencing (?:guidelines|factors|considerations)',
                    r'mitigating (?:circumstances|factors)',
                    r'aggravating (?:circumstances|factors)'
                ],
                'professional_assessments': [
                    r'witness (?:credibility|reliability) (?:assessment|evaluation)',
                    r'testimony (?:consistency|accuracy)',
                    r'evidence (?:reliability|validity)',
                    r'professional (?:opinion|assessment|judgment)',
                    r'expert (?:testimony|analysis|evaluation)',
                    r'forensic (?:analysis|evidence|report)'
                ]
            },
            'bias_indicators_in_legal_context': {
                'predictive_bias': [
                    r'we can expect (?:this type of|similar|more)',
                    r'(?:typical|usual|expected) (?:behavior|pattern|response)',
                    r'(?:people|individuals) like (?:him|her|this|that)',
                    r'(?:his|her|their) (?:kind|type|sort) (?:usually|typically|always)',
                    r'based on (?:his|her|their) (?:background|history|type)'
                ],
                'othering_language': [
                    r'(?:these|those) (?:people|individuals|types)',
                    r'people from (?:that|his|her) (?:community|background|area)',
                    r'(?:his|her|their) (?:people|community|kind|sort)',
                    r'unlike (?:normal|regular|typical|our) (?:people|individuals)',
                    r'different from (?:us|normal people|regular folks)'
                ],
                'assumption_language': [
                    r'(?:obviously|clearly|naturally) (?:he|she|they)',
                    r'of course (?:he|she|they|someone like)',
                    r'as expected from (?:someone|people) (?:like|from)',
                    r'not surprising (?:given|considering|from)',
                    r'predictably (?:he|she|they)'
                ]
            }
        }
        
        # ADVANCED SEMANTIC ANALYSIS PATTERNS
        self.semantic_analysis_patterns = {
            'implicit_assumptions': {
                'surprise_patterns': [
                    r'(?:surprisingly|unexpectedly|remarkably) (?:articulate|intelligent|well-spoken|educated)',
                    r'(?:quite|very|rather) (?:articulate|intelligent|well-spoken) for (?:someone|a person)',
                    r'speaks (?:very )?(?:good|well|excellent) (?:english|hindi) (?:despite|for|given)',
                    r'(?:more|better) than (?:expected|anticipated|usual) (?:from|for)'
                ],
                'qualification_patterns': [
                    r'(?:despite|given|considering) (?:his|her|their) (?:background|circumstances|situation)',
                    r'for someone (?:from|with|of) (?:his|her|their) (?:background|circumstances|community)',
                    r'(?:although|even though|while) (?:he|she|they) (?:is|are|comes?) from',
                    r'taking into account (?:his|her|their) (?:background|circumstances|situation)'
                ],
                'comparison_patterns': [
                    r'(?:unlike|different from) (?:others|most) (?:from|in|of) (?:his|her|their)',
                    r'(?:more|less) (?:cooperative|intelligent|articulate) than (?:typical|usual|most)',
                    r'stands out from (?:others|most) (?:in|from|of) (?:his|her|their)',
                    r'not like (?:other|most|typical) (?:people|individuals) from'
                ]
            },
            'neutral_descriptors': {
                'factual_observations': [
                    r'works (?:multiple jobs|as a|in)',
                    r'lives in (?:a|the) (?:area|neighborhood|locality)',
                    r'has (?:a|an) (?:background|education|experience) in',
                    r'comes from (?:a|the) (?:family|area|region) (?:that|where|with)',
                    r'practices (?:his|her|their) (?:religion|faith|beliefs)',
                    r'speaks (?:multiple|several|two|three) languages'
                ],
                'legitimate_considerations': [
                    r'scheduling (?:conflicts|considerations|requirements)',
                    r'language (?:preferences|requirements|barriers)',
                    r'cultural (?:practices|requirements|considerations)',
                    r'religious (?:obligations|practices|requirements)',
                    r'family (?:responsibilities|obligations|circumstances)',
                    r'work (?:schedule|commitments|obligations)'
                ]
            }
        }
        
        # ENHANCED FALSE POSITIVE FILTERS
        self.false_positive_filters = {
            'professional_observations': [
                r'(?:cash-based|informal) (?:transactions|business|work)',
                r'(?:memory|cognitive) (?:issues|concerns|considerations)',
                r'(?:physical|health) (?:limitations|considerations|factors)',
                r'(?:language|communication) (?:barriers|challenges|differences)',
                r'(?:documentation|record) (?:keeping|maintenance|availability)',
                r'(?:seasonal|temporary|part-time) (?:work|employment|income)'
            ],
            'legitimate_legal_language': [
                r'legal (?:representation|counsel|advice)',
                r'court (?:procedures|processes|requirements)',
                r'evidence (?:collection|preservation|presentation)',
                r'witness (?:preparation|protection|support)',
                r'case (?:preparation|management|strategy)',
                r'judicial (?:process|procedure|review)'
            ],
            'factual_statements': [
                r'according to (?:records|documents|reports)',
                r'based on (?:evidence|testimony|documentation)',
                r'as stated in (?:the|court|legal) (?:records|documents)',
                r'documented in (?:the|court|case) (?:files|records)',
                r'verified through (?:investigation|inquiry|research)',
                r'confirmed by (?:witnesses|evidence|documentation)'
            ]
        }
        
        # CONTEXTUAL SCORING WEIGHTS
        self.context_weights = {
            'legal_legitimacy': 0.4,
            'semantic_confidence': 0.3,
            'pattern_strength': 0.2,
            'cultural_sensitivity': 0.1
        }

    def advanced_bias_detection(self, text: str, speaker: str = None, timestamp: str = None,
                               case_context: Dict = None) -> List[AdvancedBiasDetection]:
        """
        Advanced bias detection with enhanced contextual understanding
        """
        detections = []
        text_lower = text.lower()
        
        # 1. Legal context analysis (NEW)
        legal_context_score = self._analyze_legal_context(text, text_lower, case_context)
        
        # 2. Semantic analysis with context awareness
        semantic_detections = self._advanced_semantic_analysis(text, text_lower, legal_context_score, speaker, timestamp)
        detections.extend(semantic_detections)
        
        # 3. Enhanced pattern matching with context filtering
        pattern_detections = self._context_aware_pattern_matching(text, text_lower, legal_context_score, speaker, timestamp)
        detections.extend(pattern_detections)
        
        # 4. False positive filtering (NEW)
        filtered_detections = self._apply_false_positive_filters(detections, text, text_lower)
        
        # 5. Advanced confidence calibration
        final_detections = self._advanced_confidence_calibration(filtered_detections, text, legal_context_score)
        
        return final_detections

    def _analyze_legal_context(self, text: str, text_lower: str, case_context: Dict = None) -> float:
        """
        Analyze legal context to determine legitimacy of language usage
        Returns score: 0.0 (clearly biased) to 1.0 (clearly legitimate legal language)
        """
        legitimate_score = 0.0
        bias_score = 0.0
        
        # Check for legitimate legal language
        for category, patterns in self.legal_context_database['legitimate_legal_contexts'].items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    legitimate_score += 0.2
        
        # Check for bias indicators in legal context
        for category, patterns in self.legal_context_database['bias_indicators_in_legal_context'].items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    bias_score += 0.3
        
        # Normalize scores
        legitimate_score = min(1.0, legitimate_score)
        bias_score = min(1.0, bias_score)
        
        # Calculate final legal context score
        if bias_score > legitimate_score:
            return max(0.0, 0.5 - (bias_score - legitimate_score))
        else:
            return min(1.0, 0.5 + (legitimate_score - bias_score))

    def _advanced_semantic_analysis(self, text: str, text_lower: str, legal_context_score: float,
                                   speaker: str, timestamp: str) -> List[AdvancedBiasDetection]:
        """
        Advanced semantic analysis for implicit bias detection
        """
        detections = []
        
        # Analyze implicit assumptions
        for category, patterns in self.semantic_analysis_patterns['implicit_assumptions'].items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    # Calculate semantic confidence
                    semantic_confidence = self._calculate_semantic_confidence(text, pattern, category)
                    
                    # Calculate neutrality score
                    neutrality_score = self._calculate_neutrality_score(text, text_lower)
                    
                    # Adjust confidence based on legal context
                    adjusted_confidence = semantic_confidence * (1.0 - legal_context_score * 0.5)
                    
                    if adjusted_confidence > 0.4:  # Threshold for detection
                        detection = AdvancedBiasDetection(
                            bias_type=f'semantic_{category}',
                            severity='medium' if adjusted_confidence > 0.7 else 'low',
                            confidence_score=adjusted_confidence,
                            detection_method='advanced_semantic_analysis',
                            context_analysis={
                                'pattern': pattern,
                                'category': category,
                                'semantic_indicators': self._extract_semantic_indicators(text)
                            },
                            uncertainty_score=1.0 - adjusted_confidence,
                            explanation=f"Detected implicit {category} through semantic analysis",
                            legal_context_score=legal_context_score,
                            semantic_confidence=semantic_confidence,
                            neutrality_score=neutrality_score,
                            speaker=speaker,
                            timestamp=timestamp,
                            evidence_text=text,
                            alternative_interpretations=self._generate_alternative_interpretations(category, legal_context_score),
                            recommendation=self._generate_recommendation(adjusted_confidence, legal_context_score)
                        )
                        detections.append(detection)
        
        return detections

    def _context_aware_pattern_matching(self, text: str, text_lower: str, legal_context_score: float,
                                       speaker: str, timestamp: str) -> List[AdvancedBiasDetection]:
        """
        Enhanced pattern matching with contextual awareness
        """
        detections = []
        
        # Import patterns from improved detector
        from improved_bias_detector import ImprovedBiasDetector
        improved_detector = ImprovedBiasDetector()
        
        # Run pattern detection
        pattern_detections = improved_detector._pattern_based_detection(text, text_lower, speaker, timestamp)
        
        # Convert to advanced detections with enhanced context analysis
        for detection in pattern_detections:
            # Calculate neutrality score
            neutrality_score = self._calculate_neutrality_score(text, text_lower)
            
            # Adjust confidence based on legal context and neutrality
            context_adjustment = (1.0 - legal_context_score * 0.3) * (1.0 - neutrality_score * 0.2)
            adjusted_confidence = detection.confidence_score * context_adjustment
            
            if adjusted_confidence > 0.3:  # Lower threshold for pattern matches
                advanced_detection = AdvancedBiasDetection(
                    bias_type=detection.bias_type,
                    severity=detection.severity,
                    confidence_score=adjusted_confidence,
                    detection_method='context_aware_pattern_matching',
                    context_analysis=detection.context_analysis,
                    uncertainty_score=detection.uncertainty_score,
                    explanation=detection.explanation,
                    legal_context_score=legal_context_score,
                    semantic_confidence=0.7,  # Default for pattern matches
                    neutrality_score=neutrality_score,
                    speaker=speaker,
                    timestamp=timestamp,
                    evidence_text=text,
                    alternative_interpretations=detection.alternative_interpretations,
                    recommendation=self._generate_recommendation(adjusted_confidence, legal_context_score)
                )
                detections.append(advanced_detection)
        
        return detections

    def _apply_false_positive_filters(self, detections: List[AdvancedBiasDetection], 
                                     text: str, text_lower: str) -> List[AdvancedBiasDetection]:
        """
        Apply advanced false positive filtering
        """
        filtered_detections = []
        
        for detection in detections:
            is_false_positive = False
            
            # Check against false positive patterns
            for category, patterns in self.false_positive_filters.items():
                for pattern in patterns:
                    if re.search(pattern, text_lower):
                        # Increase neutrality score
                        detection.neutrality_score = min(1.0, detection.neutrality_score + 0.3)
                        
                        # Reduce confidence if high neutrality
                        if detection.neutrality_score > 0.7:
                            detection.confidence_score *= 0.6
                            detection.explanation += f" (Adjusted for {category})"
                        
                        # Mark as potential false positive if confidence drops too low
                        if detection.confidence_score < 0.3:
                            is_false_positive = True
                            break
                
                if is_false_positive:
                    break
            
            # Keep detection if not filtered out
            if not is_false_positive:
                filtered_detections.append(detection)
        
        return filtered_detections

    def _advanced_confidence_calibration(self, detections: List[AdvancedBiasDetection], 
                                        text: str, legal_context_score: float) -> List[AdvancedBiasDetection]:
        """
        Advanced confidence calibration using multiple factors
        """
        for detection in detections:
            # Calculate weighted confidence
            weighted_confidence = (
                detection.confidence_score * self.context_weights['pattern_strength'] +
                detection.semantic_confidence * self.context_weights['semantic_confidence'] +
                (1.0 - detection.legal_context_score) * self.context_weights['legal_legitimacy'] +
                (1.0 - detection.neutrality_score) * self.context_weights['cultural_sensitivity']
            )
            
            # Update confidence and uncertainty
            detection.confidence_score = min(0.95, max(0.1, weighted_confidence))
            detection.uncertainty_score = 1.0 - detection.confidence_score
            
            # Update severity based on final confidence
            if detection.confidence_score > 0.8:
                detection.severity = 'high'
            elif detection.confidence_score > 0.6:
                detection.severity = 'medium'
            else:
                detection.severity = 'low'
        
        return detections

    # Helper methods
    def _calculate_semantic_confidence(self, text: str, pattern: str, category: str) -> float:
        """Calculate confidence based on semantic indicators"""
        # Simple implementation - can be enhanced with NLP models
        words = text.lower().split()
        semantic_indicators = ['surprisingly', 'quite', 'very', 'typical', 'usual', 'expected']
        indicator_count = sum(1 for word in words if word in semantic_indicators)
        return min(0.9, 0.5 + (indicator_count * 0.1))

    def _calculate_neutrality_score(self, text: str, text_lower: str) -> float:
        """Calculate how likely the text is neutral/legitimate"""
        neutrality_score = 0.0
        
        # Check for neutral descriptors
        for category, patterns in self.semantic_analysis_patterns['neutral_descriptors'].items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    neutrality_score += 0.2
        
        return min(1.0, neutrality_score)

    def _extract_semantic_indicators(self, text: str) -> List[str]:
        """Extract semantic indicators from text"""
        indicators = []
        words = text.lower().split()
        semantic_words = ['surprisingly', 'quite', 'very', 'typical', 'usual', 'expected', 'naturally']
        
        for word in words:
            if word in semantic_words:
                indicators.append(word)
        
        return indicators

    def _generate_alternative_interpretations(self, category: str, legal_context_score: float) -> List[str]:
        """Generate context-aware alternative interpretations"""
        base_interpretations = [
            "Could be legitimate descriptive language",
            "May indicate unconscious bias",
            "Possible intentional bias expression"
        ]
        
        if legal_context_score > 0.7:
            base_interpretations.insert(0, "Likely legitimate legal/professional language")
        elif legal_context_score < 0.3:
            base_interpretations.append("High likelihood of biased language")
        
        return base_interpretations

    def _generate_recommendation(self, confidence: float, legal_context_score: float) -> str:
        """Generate action recommendation based on confidence and context"""
        if confidence > 0.8 and legal_context_score < 0.3:
            return "HIGH PRIORITY: Immediate review recommended"
        elif confidence > 0.6:
            return "MEDIUM PRIORITY: Human review suggested"
        elif legal_context_score > 0.7:
            return "LOW PRIORITY: Likely legitimate, monitor only"
        else:
            return "MONITOR: Uncertain case, track for patterns"

# Integration function
def advanced_bias_detection(diarized_segments: List[Dict], case_context: Dict = None) -> Dict:
    """
    Advanced bias detection with enhanced contextual understanding
    """
    detector = AdvancedBiasDetector()
    all_detections = []
    
    for segment in diarized_segments:
        speaker = segment.get('speaker', 'Unknown')
        text = segment.get('text', '')
        timestamp = segment.get('timestamp', '')
        
        # Advanced detection
        detections = detector.advanced_bias_detection(text, speaker, timestamp, case_context)
        all_detections.extend(detections)
    
    return {
        'advanced_detections': all_detections,
        'total_detections': len(all_detections),
        'priority_breakdown': _calculate_priority_breakdown(all_detections),
        'confidence_analysis': _calculate_advanced_confidence_analysis(all_detections),
        'recommendations': _generate_system_recommendations(all_detections)
    }

def _calculate_priority_breakdown(detections: List[AdvancedBiasDetection]) -> Dict:
    """Calculate priority breakdown"""
    breakdown = {'high': 0, 'medium': 0, 'low': 0, 'monitor': 0}
    
    for detection in detections:
        if 'HIGH PRIORITY' in detection.recommendation:
            breakdown['high'] += 1
        elif 'MEDIUM PRIORITY' in detection.recommendation:
            breakdown['medium'] += 1
        elif 'LOW PRIORITY' in detection.recommendation:
            breakdown['low'] += 1
        else:
            breakdown['monitor'] += 1
    
    return breakdown

def _calculate_advanced_confidence_analysis(detections: List[AdvancedBiasDetection]) -> Dict:
    """Calculate advanced confidence analysis"""
    if not detections:
        return {'average_confidence': 0.0, 'average_legal_context': 0.0, 'average_neutrality': 0.0}
    
    avg_confidence = np.mean([d.confidence_score for d in detections])
    avg_legal_context = np.mean([d.legal_context_score for d in detections])
    avg_neutrality = np.mean([d.neutrality_score for d in detections])
    
    return {
        'average_confidence': avg_confidence,
        'average_legal_context': avg_legal_context,
        'average_neutrality': avg_neutrality,
        'high_confidence_count': sum(1 for d in detections if d.confidence_score > 0.8),
        'uncertain_count': sum(1 for d in detections if d.uncertainty_score > 0.6)
    }

def _generate_system_recommendations(detections: List[AdvancedBiasDetection]) -> List[str]:
    """Generate system-level recommendations"""
    recommendations = []
    
    high_priority = sum(1 for d in detections if 'HIGH PRIORITY' in d.recommendation)
    if high_priority > 0:
        recommendations.append(f"Immediate attention required: {high_priority} high-priority bias detections")
    
    uncertain = sum(1 for d in detections if d.uncertainty_score > 0.6)
    if uncertain > 2:
        recommendations.append(f"Consider human review for {uncertain} uncertain cases")
    
    if len(detections) == 0:
        recommendations.append("No bias detected - proceedings appear neutral")
    
    return recommendations

if __name__ == "__main__":
    print("🚀 Advanced Bias Detection System - Next Iteration")
    print("Enhanced with contextual understanding and false positive reduction")
