{"test_summary": {"total_cases": 40, "overall_detection_rate": 82.5, "precision": 90.9090909090909, "false_positives": 3, "categories_tested": 10}, "category_performance": {"subtle_implicit_bias": {"detection_rate": 100.0, "total": 4, "detected": 4, "missed": 0, "false_positives": 0}, "intersectional_bias": {"detection_rate": 75.0, "total": 4, "detected": 3, "missed": 1, "false_positives": 0}, "contextual_ambiguity": {"detection_rate": 25.0, "total": 4, "detected": 1, "missed": 3, "false_positives": 0}, "coded_language": {"detection_rate": 100.0, "total": 4, "detected": 4, "missed": 0, "false_positives": 0}, "regional_cultural": {"detection_rate": 75.0, "total": 4, "detected": 3, "missed": 1, "false_positives": 0}, "professional_bias": {"detection_rate": 100.0, "total": 4, "detected": 4, "missed": 0, "false_positives": 0}, "gender_bias": {"detection_rate": 100.0, "total": 4, "detected": 4, "missed": 0, "false_positives": 0}, "economic_bias": {"detection_rate": 75.0, "total": 4, "detected": 3, "missed": 1, "false_positives": 0}, "religious_bias": {"detection_rate": 75.0, "total": 4, "detected": 3, "missed": 1, "false_positives": 0}, "age_bias": {"detection_rate": 100.0, "total": 4, "detected": 4, "missed": 0, "false_positives": 0}}, "difficulty_analysis": {"low": {"total": 3, "detected": 2}, "medium": {"total": 23, "detected": 20}, "high": {"total": 14, "detected": 11}}, "improvement_recommendations": [{"priority": "high", "area": "Contextual Ambiguity", "issue": "Very low detection rate (25.0%)", "recommendation": "Enhance pattern recognition for contextual_ambiguity scenarios", "technical_approach": "Add advanced context classification and domain knowledge"}], "detailed_results": [{"category": "subtle_implicit_bias", "text": "The witness seems quite articulate for someone from his background.", "expected_type": "implicit_assumption", "difficulty": "high", "detected": true, "detections": 3, "is_correct": true}, {"category": "subtle_implicit_bias", "text": "She's surprisingly well-educated given her family circumstances.", "expected_type": "implicit_assumption", "difficulty": "high", "detected": true, "detections": 3, "is_correct": true}, {"category": "subtle_implicit_bias", "text": "He speaks English very well despite being from a rural area.", "expected_type": "geographic_bias", "difficulty": "medium", "detected": true, "detections": 2, "is_correct": true}, {"category": "subtle_implicit_bias", "text": "The defendant is more cooperative than we usually see from his community.", "expected_type": "community_stereotyping", "difficulty": "medium", "detected": true, "detections": 1, "is_correct": true}, {"category": "intersectional_bias", "text": "Young Muslim men from poor families often get involved in such activities.", "expected_type": "intersectional", "difficulty": "medium", "detected": true, "detections": 2, "is_correct": true}, {"category": "intersectional_bias", "text": "Elderly Dalit women are usually more submissive in court proceedings.", "expected_type": "intersectional", "difficulty": "medium", "detected": true, "detections": 1, "is_correct": true}, {"category": "intersectional_bias", "text": "Educated upper-caste women typically handle cross-examination better.", "expected_type": "intersectional", "difficulty": "high", "detected": true, "detections": 1, "is_correct": true}, {"category": "intersectional_bias", "text": "Tribal youth from remote areas struggle with urban legal concepts.", "expected_type": "intersectional", "difficulty": "high", "detected": false, "detections": 0, "is_correct": false}, {"category": "contextual_ambiguity", "text": "The defendant's criminal history includes three prior convictions for theft.", "expected_type": "neutral", "difficulty": "low", "detected": false, "detections": 0, "is_correct": true}, {"category": "contextual_ambiguity", "text": "Given his criminal history, we can expect this type of behavior to continue.", "expected_type": "predictive_bias", "difficulty": "medium", "detected": true, "detections": 1, "is_correct": true}, {"category": "contextual_ambiguity", "text": "Community standards require us to consider the impact on local families.", "expected_type": "neutral", "difficulty": "medium", "detected": false, "detections": 0, "is_correct": true}, {"category": "contextual_ambiguity", "text": "People from that community don't share our community standards.", "expected_type": "cultural_othering", "difficulty": "medium", "detected": false, "detections": 0, "is_correct": false}, {"category": "coded_language", "text": "The defendant has that inner-city vibe, if you know what I mean.", "expected_type": "coded_racial", "difficulty": "medium", "detected": true, "detections": 1, "is_correct": true}, {"category": "coded_language", "text": "She has a very cosmopolitan outlook, unlike others from her area.", "expected_type": "coded_class", "difficulty": "high", "detected": true, "detections": 1, "is_correct": true}, {"category": "coded_language", "text": "The witness displays typical characteristics of his demographic.", "expected_type": "demographic_stereotyping", "difficulty": "medium", "detected": true, "detections": 1, "is_correct": true}, {"category": "coded_language", "text": "He has adopted a more progressive mindset recently.", "expected_type": "ambiguous", "difficulty": "high", "detected": true, "detections": 1, "is_correct": true}, {"category": "regional_cultural", "text": "These Punjabis are always involved in such disputes.", "expected_type": "regional_stereotyping", "difficulty": "low", "detected": true, "detections": 2, "is_correct": true}, {"category": "regional_cultural", "text": "The witness has that typical Gujarati business mentality.", "expected_type": "regional_occupational", "difficulty": "medium", "detected": false, "detections": 0, "is_correct": false}, {"category": "regional_cultural", "text": "Bengali intellectuals often overthink simple legal matters.", "expected_type": "regional_intellectual", "difficulty": "medium", "detected": true, "detections": 1, "is_correct": true}, {"category": "regional_cultural", "text": "People from the Northeast have different cultural values.", "expected_type": "regional_othering", "difficulty": "medium", "detected": true, "detections": 1, "is_correct": true}], "next_steps": ["Focus on low-performing categories", "Reduce false positive rate", "Improve high-difficulty case handling", "Enhance context understanding", "Expand cultural and regional knowledge"]}