"""
Test Enhanced Bias Detection System - Addressing Critical Limitations
Demonstrates improvements in: Novel Bias, Context Understanding, Cultural Context, Training Data Bias
"""

from enhanced_bias_detector import enhanced_bias_detection, EnhancedBiasDetector
import json

def test_comprehensive_scenarios():
    """Test enhanced model across variety of scenarios for further improvements"""

    print("🧪 COMPREHENSIVE SCENARIO TESTING - ENHANCED MODEL")
    print("=" * 70)
    print("Testing diverse scenarios to identify improvement opportunities...")
    print("=" * 70)

    # Comprehensive test scenarios covering edge cases and real-world situations
    test_scenarios = [
        {
            'category': 'SUBTLE IMPLICIT BIAS',
            'description': 'Testing detection of very subtle, indirect bias',
            'cases': [
                {
                    'text': 'The witness seems quite articulate for someone from his background.',
                    'expected_detection': 'Should detect implicit assumption about background vs articulation',
                    'difficulty': 'high'
                },
                {
                    'text': 'She\'s surprisingly well-educated given her family circumstances.',
                    'expected_detection': 'Should detect assumption about family circumstances vs education',
                    'difficulty': 'high'
                },
                {
                    'text': 'He speaks English very well despite being from a rural area.',
                    'expected_detection': 'Should detect rural vs English proficiency assumption',
                    'difficulty': 'medium'
                },
                {
                    'text': 'The defendant is more cooperative than we usually see from his community.',
                    'expected_detection': 'Should detect community-based behavioral assumptions',
                    'difficulty': 'medium'
                }
            ]
        },
        {
            'category': 'INTERSECTIONAL BIAS COMPLEXITY',
            'description': 'Testing complex intersectional bias scenarios',
            'cases': [
                {
                    'text': 'Young Muslim men from poor families often get involved in such activities.',
                    'expected_detection': 'Should detect age + religion + economic + gender intersection',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Elderly Dalit women are usually more submissive in court proceedings.',
                    'expected_detection': 'Should detect age + caste + gender intersection',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Educated upper-caste women typically handle cross-examination better.',
                    'expected_detection': 'Should detect education + caste + gender intersection',
                    'difficulty': 'high'
                },
                {
                    'text': 'Tribal youth from remote areas struggle with urban legal concepts.',
                    'expected_detection': 'Should detect ethnicity + age + geography + class intersection',
                    'difficulty': 'high'
                }
            ]
        },
        {
            'category': 'CONTEXTUAL AMBIGUITY',
            'description': 'Testing scenarios where context determines bias vs legitimate usage',
            'cases': [
                {
                    'text': 'The defendant\'s criminal history includes three prior convictions for theft.',
                    'expected_detection': 'Should recognize legitimate factual statement',
                    'difficulty': 'low'
                },
                {
                    'text': 'Given his criminal history, we can expect this type of behavior to continue.',
                    'expected_detection': 'Should detect biased prediction based on history',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Community standards require us to consider the impact on local families.',
                    'expected_detection': 'Should recognize legitimate legal consideration',
                    'difficulty': 'medium'
                },
                {
                    'text': 'People from that community don\'t share our community standards.',
                    'expected_detection': 'Should detect othering and cultural bias',
                    'difficulty': 'medium'
                }
            ]
        },
        {
            'category': 'CODED LANGUAGE EVOLUTION',
            'description': 'Testing newer forms of coded language and euphemisms',
            'cases': [
                {
                    'text': 'The defendant has that inner-city vibe, if you know what I mean.',
                    'expected_detection': 'Should detect coded racial/class language',
                    'difficulty': 'medium'
                },
                {
                    'text': 'She has a very cosmopolitan outlook, unlike others from her area.',
                    'expected_detection': 'Should detect coded class/cultural bias',
                    'difficulty': 'high'
                },
                {
                    'text': 'He seems to have adopted a more progressive mindset recently.',
                    'expected_detection': 'May be neutral or coded - context dependent',
                    'difficulty': 'high'
                },
                {
                    'text': 'The witness displays typical characteristics of his demographic.',
                    'expected_detection': 'Should detect demographic stereotyping',
                    'difficulty': 'medium'
                }
            ]
        },
        {
            'category': 'REGIONAL VARIATIONS',
            'description': 'Testing regional and linguistic variations across India',
            'cases': [
                {
                    'text': 'These Punjabis are always involved in such disputes.',
                    'expected_detection': 'Should detect regional stereotyping',
                    'difficulty': 'low'
                },
                {
                    'text': 'The witness has that typical Gujarati business mentality.',
                    'expected_detection': 'Should detect regional + occupational stereotyping',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Bengali intellectuals often overthink simple legal matters.',
                    'expected_detection': 'Should detect regional + intellectual stereotyping',
                    'difficulty': 'medium'
                },
                {
                    'text': 'People from the Northeast have different cultural values.',
                    'expected_detection': 'Should detect regional othering',
                    'difficulty': 'medium'
                }
            ]
        },
        {
            'category': 'PROFESSIONAL BIAS',
            'description': 'Testing bias related to professions and occupations',
            'cases': [
                {
                    'text': 'As a taxi driver, he probably doesn\'t understand complex legal procedures.',
                    'expected_detection': 'Should detect occupational bias about intelligence',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Being a domestic worker, she might not be reliable with time commitments.',
                    'expected_detection': 'Should detect occupational bias about reliability',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Farmers from rural areas often have difficulty with paperwork.',
                    'expected_detection': 'Should detect occupational + geographic bias',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Street vendors typically have cash-based transactions, making records unclear.',
                    'expected_detection': 'Should recognize legitimate occupational observation',
                    'difficulty': 'high'
                }
            ]
        },
        {
            'category': 'GENDER BIAS SUBTLETIES',
            'description': 'Testing subtle forms of gender bias',
            'cases': [
                {
                    'text': 'The female witness seems quite emotional about the incident.',
                    'expected_detection': 'Should detect gender stereotype about emotionality',
                    'difficulty': 'medium'
                },
                {
                    'text': 'As a mother, she would naturally want to protect her family.',
                    'expected_detection': 'Should detect gender role assumption',
                    'difficulty': 'high'
                },
                {
                    'text': 'The male defendant appears more aggressive than typical female defendants.',
                    'expected_detection': 'Should detect gender-based behavioral assumptions',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Women from traditional families might not speak up in court.',
                    'expected_detection': 'Should detect gender + cultural intersection',
                    'difficulty': 'medium'
                }
            ]
        },
        {
            'category': 'AGE-RELATED BIAS',
            'description': 'Testing age-based assumptions and stereotypes',
            'cases': [
                {
                    'text': 'Young people today don\'t respect authority like previous generations.',
                    'expected_detection': 'Should detect generational bias',
                    'difficulty': 'medium'
                },
                {
                    'text': 'At his age, he should know better than to get involved in such activities.',
                    'expected_detection': 'Should detect age-based moral assumptions',
                    'difficulty': 'high'
                },
                {
                    'text': 'Elderly witnesses sometimes have memory issues that affect testimony.',
                    'expected_detection': 'Should recognize legitimate age-related consideration',
                    'difficulty': 'high'
                },
                {
                    'text': 'Teenagers from broken homes often end up in trouble.',
                    'expected_detection': 'Should detect age + family structure bias',
                    'difficulty': 'medium'
                }
            ]
        },
        {
            'category': 'ECONOMIC STATUS BIAS',
            'description': 'Testing bias related to economic and social class',
            'cases': [
                {
                    'text': 'People from slum areas are more likely to be involved in petty crimes.',
                    'expected_detection': 'Should detect economic geography bias',
                    'difficulty': 'low'
                },
                {
                    'text': 'The defendant cannot afford a good lawyer, which explains his situation.',
                    'expected_detection': 'Should detect economic bias about legal representation',
                    'difficulty': 'medium'
                },
                {
                    'text': 'Wealthy families usually have better character witnesses.',
                    'expected_detection': 'Should detect economic bias about character',
                    'difficulty': 'medium'
                },
                {
                    'text': 'The witness works multiple jobs to support her family.',
                    'expected_detection': 'Should recognize neutral economic description',
                    'difficulty': 'high'
                }
            ]
        },
        {
            'category': 'RELIGIOUS BIAS NUANCES',
            'description': 'Testing subtle religious bias and assumptions',
            'cases': [
                {
                    'text': 'His religious practices might influence his testimony timing.',
                    'expected_detection': 'Should recognize legitimate scheduling consideration',
                    'difficulty': 'high'
                },
                {
                    'text': 'People of his faith often have different concepts of truth-telling.',
                    'expected_detection': 'Should detect religious bias about honesty',
                    'difficulty': 'medium'
                },
                {
                    'text': 'The defendant\'s religious background suggests strong moral values.',
                    'expected_detection': 'Should detect positive religious stereotyping',
                    'difficulty': 'high'
                },
                {
                    'text': 'Minority religious communities often face discrimination in such cases.',
                    'expected_detection': 'Should recognize legitimate discrimination observation',
                    'difficulty': 'high'
                }
            ]
        }
    ]

    return test_scenarios

def test_critical_limitation_solutions():
    """Test how enhanced system addresses each critical limitation"""

    print("🔧 TESTING ENHANCED SYSTEM - CRITICAL LIMITATION SOLUTIONS")
    print("=" * 70)
    
    # Test cases specifically designed to test each critical limitation
    test_scenarios = [
        {
            'limitation': 'CRITICAL LIMITATION 1: Novel Bias Expressions',
            'description': 'Testing detection of coded language, euphemisms, and dog whistles',
            'segments': [
                {
                    'speaker': 'Judge',
                    'text': 'The defendant has that typical urban mindset, you know what I mean.',
                    'timestamp': '00:01:00',
                    'expected': 'Should detect coded language "urban mindset"'
                },
                {
                    'speaker': 'Prosecutor',
                    'text': 'People with alternative lifestyles often have different values than our traditional culture.',
                    'timestamp': '00:02:00',
                    'expected': 'Should detect euphemisms and dog whistles'
                },
                {
                    'speaker': 'Judge',
                    'text': 'Those people from that type of background usually have limited understanding.',
                    'timestamp': '00:03:00',
                    'expected': 'Should detect coded class bias'
                }
            ]
        },
        {
            'limitation': 'CRITICAL LIMITATION 2: Context Understanding',
            'description': 'Testing context-aware detection to reduce false positives',
            'segments': [
                {
                    'speaker': 'Judge',
                    'text': 'Given the defendant\'s criminal background, the sentence should reflect the severity of the crime.',
                    'timestamp': '00:01:00',
                    'expected': 'Should recognize legitimate legal context'
                },
                {
                    'speaker': 'Prosecutor',
                    'text': 'The defendant has a criminal background, which shows his typical criminal mentality.',
                    'timestamp': '00:02:00',
                    'expected': 'Should detect biased usage of legal term'
                },
                {
                    'speaker': 'Judge',
                    'text': 'We must consider community standards in evaluating this case.',
                    'timestamp': '00:03:00',
                    'expected': 'Should recognize legitimate legal concept'
                }
            ]
        },
        {
            'limitation': 'CRITICAL LIMITATION 3: Cultural Context Understanding',
            'description': 'Testing enhanced cultural and regional bias detection',
            'segments': [
                {
                    'speaker': 'Prosecutor',
                    'text': 'These Biharis always have that typical mentality, you know how they are.',
                    'timestamp': '00:01:00',
                    'expected': 'Should detect regional bias with cultural context'
                },
                {
                    'speaker': 'Judge',
                    'text': 'His people from that community usually behave this way.',
                    'timestamp': '00:02:00',
                    'expected': 'Should detect caste-coded language'
                },
                {
                    'speaker': 'Lawyer',
                    'text': 'The witness is from Tamil Nadu and speaks Tamil.',
                    'timestamp': '00:03:00',
                    'expected': 'Should recognize neutral regional reference'
                }
            ]
        },
        {
            'limitation': 'CRITICAL LIMITATION 4: Training Data Bias',
            'description': 'Testing enhanced detection for underrepresented groups and intersectionality',
            'segments': [
                {
                    'speaker': 'Judge',
                    'text': 'These tribal people don\'t understand modern legal concepts.',
                    'timestamp': '00:01:00',
                    'expected': 'Should detect bias against underrepresented tribal community'
                },
                {
                    'speaker': 'Prosecutor',
                    'text': 'Poor Muslim women are often influenced by their community pressure.',
                    'timestamp': '00:02:00',
                    'expected': 'Should detect intersectional bias (economic + religious + gender)'
                },
                {
                    'speaker': 'Judge',
                    'text': 'Young Dalit men typically have aggressive tendencies.',
                    'timestamp': '00:03:00',
                    'expected': 'Should detect intersectional bias (age + caste + gender)'
                }
            ]
        }
    ]
    
    detector = EnhancedBiasDetector()
    
    for scenario in test_scenarios:
        print(f"\n🎯 {scenario['limitation']}")
        print(f"Description: {scenario['description']}")
        print("-" * 60)
        
        for i, segment in enumerate(scenario['segments'], 1):
            print(f"\nTest {i}: {segment['speaker']}")
            print(f"Text: '{segment['text']}'")
            print(f"Expected: {segment['expected']}")
            
            # Test with enhanced detector
            detections = detector.enhanced_bias_detection(
                segment['text'], 
                segment['speaker'], 
                segment['timestamp']
            )
            
            if detections:
                print("✅ ENHANCED DETECTION RESULTS:")
                for detection in detections:
                    print(f"  • Type: {detection.bias_type}")
                    print(f"  • Method: {detection.detection_method}")
                    print(f"  • Confidence: {detection.confidence_score:.2f}")
                    print(f"  • Uncertainty: {detection.uncertainty_score:.2f}")
                    print(f"  • Explanation: {detection.explanation}")
                    print(f"  • Alternatives: {len(detection.alternative_interpretations)} interpretations")
            else:
                print("❌ No bias detected")

def test_enhanced_vs_original_comparison():
    """Compare enhanced system with original system"""
    
    print(f"\n\n📊 ENHANCED vs ORIGINAL SYSTEM COMPARISON")
    print("=" * 60)
    
    # Test cases that highlight improvements
    comparison_cases = [
        {
            'case': 'Novel Coded Language',
            'text': 'The defendant has that typical urban mindset and street mentality.',
            'original_expected': 'May miss coded language',
            'enhanced_expected': 'Should detect coded bias'
        },
        {
            'case': 'Context-Dependent Legal Term',
            'text': 'Given his criminal background, he has that typical criminal nature.',
            'original_expected': 'May flag legitimate legal term',
            'enhanced_expected': 'Should detect biased usage while recognizing legal context'
        },
        {
            'case': 'Cultural Regional Reference',
            'text': 'These Biharis have that typical migrant mentality.',
            'original_expected': 'Basic regional bias detection',
            'enhanced_expected': 'Enhanced cultural context understanding'
        },
        {
            'case': 'Intersectional Bias',
            'text': 'Young tribal women are usually influenced by traditional customs.',
            'original_expected': 'May detect only one dimension',
            'enhanced_expected': 'Should detect intersectional bias'
        }
    ]
    
    # Import original system for comparison
    from comprehensive_bias_detector import comprehensive_bias_detection
    
    for case in comparison_cases:
        print(f"\n🔍 {case['case']}:")
        print(f"Text: '{case['text']}'")
        
        # Test with original system
        original_segments = [{'speaker': 'Test', 'text': case['text'], 'timestamp': '00:01:00'}]
        original_results = comprehensive_bias_detection(original_segments, enable_ml=False)
        
        # Test with enhanced system
        enhanced_results = enhanced_bias_detection(original_segments)
        
        print(f"\n  📈 COMPARISON RESULTS:")
        print(f"    Original System: {original_results['summary']['total_bias_instances']} detections")
        print(f"    Enhanced System: {enhanced_results['total_detections']} detections")
        
        if enhanced_results['enhanced_detections']:
            enhanced_detection = enhanced_results['enhanced_detections'][0]
            print(f"    Enhanced Features:")
            print(f"      • Uncertainty Score: {enhanced_detection.uncertainty_score:.2f}")
            print(f"      • Detailed Explanation: Available")
            print(f"      • Alternative Interpretations: {len(enhanced_detection.alternative_interpretations)}")
            print(f"      • Cultural Context: {enhanced_detection.cultural_context or 'N/A'}")

def test_uncertainty_quantification():
    """Test uncertainty quantification feature"""
    
    print(f"\n\n🎲 UNCERTAINTY QUANTIFICATION TESTING")
    print("=" * 60)
    
    uncertainty_test_cases = [
        {
            'case': 'High Certainty',
            'text': 'All Muslims are terrorists and cannot be trusted.',
            'expected_uncertainty': 'Low (high confidence in detection)'
        },
        {
            'case': 'Medium Certainty',
            'text': 'People from that community often have different values.',
            'expected_uncertainty': 'Medium (coded language ambiguity)'
        },
        {
            'case': 'High Uncertainty',
            'text': 'The defendant seems to have an urban background.',
            'expected_uncertainty': 'High (ambiguous coded language)'
        }
    ]
    
    detector = EnhancedBiasDetector()
    
    for case in uncertainty_test_cases:
        print(f"\n📊 {case['case']}:")
        print(f"Text: '{case['text']}'")
        print(f"Expected: {case['expected_uncertainty']}")
        
        detections = detector.enhanced_bias_detection(case['text'], 'Test_Speaker')
        
        if detections:
            detection = detections[0]
            print(f"Results:")
            print(f"  • Confidence: {detection.confidence_score:.2f}")
            print(f"  • Uncertainty: {detection.uncertainty_score:.2f}")
            print(f"  • Explanation: {detection.explanation}")
            
            # Interpret uncertainty level
            if detection.uncertainty_score < 0.3:
                uncertainty_level = "Low"
            elif detection.uncertainty_score < 0.6:
                uncertainty_level = "Medium"
            else:
                uncertainty_level = "High"
            
            print(f"  • Uncertainty Level: {uncertainty_level}")
        else:
            print("  • No bias detected")

def test_alternative_interpretations():
    """Test alternative interpretations feature"""
    
    print(f"\n\n🔄 ALTERNATIVE INTERPRETATIONS TESTING")
    print("=" * 60)
    
    interpretation_cases = [
        'The defendant has that typical urban mindset.',
        'Given his background, this behavior is expected.',
        'People from that community have different values.'
    ]
    
    detector = EnhancedBiasDetector()
    
    for i, text in enumerate(interpretation_cases, 1):
        print(f"\nCase {i}: '{text}'")
        
        detections = detector.enhanced_bias_detection(text, 'Test_Speaker')
        
        if detections:
            detection = detections[0]
            print(f"Detection: {detection.bias_type}")
            print(f"Alternative Interpretations:")
            for j, interpretation in enumerate(detection.alternative_interpretations, 1):
                print(f"  {j}. {interpretation}")
        else:
            print("No bias detected")

def save_enhanced_test_results():
    """Save enhanced system test results"""
    
    results_summary = {
        'enhanced_system_capabilities': {
            'novel_bias_detection': 'Detects coded language, euphemisms, dog whistles',
            'context_awareness': 'Distinguishes legitimate legal terms from biased usage',
            'cultural_context': 'Enhanced regional and cultural bias understanding',
            'training_bias_mitigation': 'Improved detection for underrepresented groups',
            'uncertainty_quantification': 'Provides uncertainty scores for each detection',
            'alternative_interpretations': 'Offers multiple interpretation possibilities',
            'detailed_explanations': 'Provides clear explanations for each detection'
        },
        'critical_limitations_addressed': {
            'limitation_1': 'Novel bias expressions - ADDRESSED with coded language detection',
            'limitation_2': 'Context understanding - ADDRESSED with legal context analysis',
            'limitation_3': 'Cultural context - ADDRESSED with enhanced cultural database',
            'limitation_4': 'Training data bias - ADDRESSED with bias mitigation strategies'
        },
        'deployment_improvements': {
            'reduced_false_positives': 'Context-aware filtering reduces inappropriate flags',
            'enhanced_coverage': 'Better detection of subtle and coded bias',
            'uncertainty_awareness': 'System knows when it\'s uncertain',
            'explainable_ai': 'Clear explanations for all detections',
            'cultural_sensitivity': 'Better handling of Indian cultural contexts'
        }
    }
    
    try:
        with open('enhanced_system_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_summary, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Enhanced system test results saved to: enhanced_system_test_results.json")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")

def run_comprehensive_scenario_testing():
    """Run comprehensive testing across all scenario categories"""

    print("\n🧪 RUNNING COMPREHENSIVE SCENARIO TESTING")
    print("=" * 70)

    scenarios = test_comprehensive_scenarios()
    detector = EnhancedBiasDetector()

    # Track results for analysis
    results_analysis = {
        'total_cases': 0,
        'detected_cases': 0,
        'missed_cases': 0,
        'category_performance': {},
        'difficulty_performance': {'low': {'total': 0, 'detected': 0},
                                 'medium': {'total': 0, 'detected': 0},
                                 'high': {'total': 0, 'detected': 0}},
        'improvement_areas': []
    }

    for scenario in scenarios:
        category = scenario['category']
        print(f"\n📂 {category}")
        print(f"Description: {scenario['description']}")
        print("-" * 50)

        category_results = {'total': 0, 'detected': 0, 'missed': 0}

        for i, case in enumerate(scenario['cases'], 1):
            print(f"\nTest {i} (Difficulty: {case['difficulty'].upper()}):")
            print(f"Text: '{case['text']}'")
            print(f"Expected: {case['expected_detection']}")

            # Test with enhanced detector
            detections = detector.enhanced_bias_detection(case['text'], 'Test_Speaker')

            results_analysis['total_cases'] += 1
            category_results['total'] += 1
            results_analysis['difficulty_performance'][case['difficulty']]['total'] += 1

            if detections:
                print("✅ DETECTION RESULTS:")
                results_analysis['detected_cases'] += 1
                category_results['detected'] += 1
                results_analysis['difficulty_performance'][case['difficulty']]['detected'] += 1

                for detection in detections[:2]:  # Show top 2 detections
                    print(f"  • Type: {detection.bias_type}")
                    print(f"  • Confidence: {detection.confidence_score:.2f}")
                    print(f"  • Uncertainty: {detection.uncertainty_score:.2f}")
                    print(f"  • Method: {detection.detection_method}")
                    print(f"  • Explanation: {detection.explanation[:80]}...")

                if len(detections) > 2:
                    print(f"  • ... and {len(detections) - 2} more detections")
            else:
                print("❌ NO BIAS DETECTED")
                results_analysis['missed_cases'] += 1
                category_results['missed'] += 1

                # Identify potential improvement areas
                if case['difficulty'] in ['low', 'medium']:
                    results_analysis['improvement_areas'].append({
                        'category': category,
                        'text': case['text'],
                        'expected': case['expected_detection'],
                        'difficulty': case['difficulty']
                    })

        # Calculate category performance
        detection_rate = (category_results['detected'] / category_results['total']) * 100
        results_analysis['category_performance'][category] = {
            'detection_rate': detection_rate,
            'total_cases': category_results['total'],
            'detected': category_results['detected'],
            'missed': category_results['missed']
        }

        print(f"\n📊 {category} PERFORMANCE:")
        print(f"   Detection Rate: {detection_rate:.1f}% ({category_results['detected']}/{category_results['total']})")

    return results_analysis

def analyze_performance_gaps(results_analysis):
    """Analyze performance gaps and suggest improvements"""

    print(f"\n\n📈 PERFORMANCE ANALYSIS & IMPROVEMENT OPPORTUNITIES")
    print("=" * 70)

    # Overall performance
    overall_detection_rate = (results_analysis['detected_cases'] / results_analysis['total_cases']) * 100
    print(f"\n🎯 OVERALL PERFORMANCE:")
    print(f"   Total Cases Tested: {results_analysis['total_cases']}")
    print(f"   Cases Detected: {results_analysis['detected_cases']}")
    print(f"   Cases Missed: {results_analysis['missed_cases']}")
    print(f"   Overall Detection Rate: {overall_detection_rate:.1f}%")

    # Performance by difficulty
    print(f"\n📊 PERFORMANCE BY DIFFICULTY:")
    for difficulty, stats in results_analysis['difficulty_performance'].items():
        if stats['total'] > 0:
            rate = (stats['detected'] / stats['total']) * 100
            print(f"   {difficulty.upper()}: {rate:.1f}% ({stats['detected']}/{stats['total']})")

    # Performance by category
    print(f"\n📂 PERFORMANCE BY CATEGORY:")
    sorted_categories = sorted(results_analysis['category_performance'].items(),
                              key=lambda x: x[1]['detection_rate'])

    for category, stats in sorted_categories:
        print(f"   {category}: {stats['detection_rate']:.1f}% ({stats['detected']}/{stats['total_cases']})")

    # Identify improvement priorities
    print(f"\n🔧 IMPROVEMENT PRIORITIES:")

    # Categories with low performance
    low_performance_categories = [cat for cat, stats in results_analysis['category_performance'].items()
                                 if stats['detection_rate'] < 50]

    if low_performance_categories:
        print(f"\n   🚨 LOW PERFORMANCE CATEGORIES (< 50% detection):")
        for category in low_performance_categories:
            stats = results_analysis['category_performance'][category]
            print(f"     • {category}: {stats['detection_rate']:.1f}%")

    # High-priority missed cases
    print(f"\n   ⚠️ HIGH-PRIORITY MISSED CASES:")
    high_priority_missed = [case for case in results_analysis['improvement_areas']
                           if case['difficulty'] in ['low', 'medium']]

    for i, case in enumerate(high_priority_missed[:5], 1):  # Show top 5
        print(f"     {i}. [{case['category']}] '{case['text'][:60]}...'")
        print(f"        Expected: {case['expected']}")
        print(f"        Difficulty: {case['difficulty']}")

    return {
        'overall_detection_rate': overall_detection_rate,
        'low_performance_categories': low_performance_categories,
        'high_priority_missed': high_priority_missed,
        'improvement_recommendations': generate_improvement_recommendations(results_analysis)
    }

def generate_improvement_recommendations(results_analysis):
    """Generate specific improvement recommendations"""

    recommendations = []

    # Analyze category performance
    category_performance = results_analysis['category_performance']

    if 'SUBTLE IMPLICIT BIAS' in category_performance and category_performance['SUBTLE IMPLICIT BIAS']['detection_rate'] < 50:
        recommendations.append({
            'area': 'Subtle Implicit Bias Detection',
            'priority': 'high',
            'recommendation': 'Implement advanced semantic analysis for implicit assumptions',
            'technical_solution': 'Add assumption pattern detection and semantic relationship analysis'
        })

    if 'CONTEXTUAL AMBIGUITY' in category_performance and category_performance['CONTEXTUAL AMBIGUITY']['detection_rate'] < 70:
        recommendations.append({
            'area': 'Context Understanding',
            'priority': 'high',
            'recommendation': 'Enhance legal context analysis and situational awareness',
            'technical_solution': 'Implement advanced context classification and legal domain knowledge'
        })

    if 'INTERSECTIONAL BIAS COMPLEXITY' in category_performance and category_performance['INTERSECTIONAL BIAS COMPLEXITY']['detection_rate'] < 80:
        recommendations.append({
            'area': 'Intersectionality Detection',
            'priority': 'medium',
            'recommendation': 'Improve compound identity bias recognition',
            'technical_solution': 'Enhance intersectionality pattern matching and compound bias scoring'
        })

    # Analyze difficulty performance
    difficulty_performance = results_analysis['difficulty_performance']

    if difficulty_performance['high']['total'] > 0 and (difficulty_performance['high']['detected'] / difficulty_performance['high']['total']) < 0.3:
        recommendations.append({
            'area': 'High Difficulty Cases',
            'priority': 'medium',
            'recommendation': 'Develop advanced pattern recognition for complex bias scenarios',
            'technical_solution': 'Implement machine learning models for sophisticated bias pattern recognition'
        })

    return recommendations

def save_comprehensive_test_results(results_analysis, performance_analysis):
    """Save comprehensive test results and recommendations"""

    comprehensive_results = {
        'test_summary': {
            'total_cases': results_analysis['total_cases'],
            'overall_detection_rate': performance_analysis['overall_detection_rate'],
            'categories_tested': len(results_analysis['category_performance']),
            'improvement_areas_identified': len(performance_analysis['high_priority_missed'])
        },
        'category_performance': results_analysis['category_performance'],
        'difficulty_analysis': results_analysis['difficulty_performance'],
        'improvement_recommendations': performance_analysis['improvement_recommendations'],
        'high_priority_missed_cases': performance_analysis['high_priority_missed'][:10],  # Top 10
        'next_development_priorities': [
            'Enhance subtle implicit bias detection',
            'Improve contextual ambiguity resolution',
            'Strengthen intersectionality recognition',
            'Add advanced semantic analysis capabilities',
            'Implement domain-specific knowledge integration'
        ]
    }

    try:
        with open('comprehensive_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Comprehensive test results saved to: comprehensive_test_results.json")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")

    return comprehensive_results

def main():
    """Main test function"""

    print("🚀 COMPREHENSIVE ENHANCED BIAS DETECTION TESTING")
    print("=" * 80)
    print("Testing enhanced model across variety of scenarios for further improvements...")
    print("=" * 80)

    # Run comprehensive scenario testing
    results_analysis = run_comprehensive_scenario_testing()

    # Analyze performance gaps
    performance_analysis = analyze_performance_gaps(results_analysis)

    # Save comprehensive results
    comprehensive_results = save_comprehensive_test_results(results_analysis, performance_analysis)

    # Display final summary
    print(f"\n\n� COMPREHENSIVE TESTING COMPLETE!")
    print("=" * 80)
    print(f"📊 OVERALL RESULTS:")
    print(f"   • Total Scenarios Tested: {results_analysis['total_cases']}")
    print(f"   • Overall Detection Rate: {performance_analysis['overall_detection_rate']:.1f}%")
    print(f"   • Categories Analyzed: {len(results_analysis['category_performance'])}")

    print(f"\n🎯 TOP PERFORMING CATEGORIES:")
    top_categories = sorted(results_analysis['category_performance'].items(),
                           key=lambda x: x[1]['detection_rate'], reverse=True)[:3]
    for i, (category, stats) in enumerate(top_categories, 1):
        print(f"   {i}. {category}: {stats['detection_rate']:.1f}%")

    print(f"\n⚠️ IMPROVEMENT NEEDED:")
    if performance_analysis['low_performance_categories']:
        for category in performance_analysis['low_performance_categories'][:3]:
            stats = results_analysis['category_performance'][category]
            print(f"   • {category}: {stats['detection_rate']:.1f}%")

    print(f"\n🔧 NEXT DEVELOPMENT PRIORITIES:")
    for i, rec in enumerate(performance_analysis['improvement_recommendations'][:3], 1):
        print(f"   {i}. {rec['area']} ({rec['priority']} priority)")
        print(f"      → {rec['recommendation']}")

    print("=" * 80)
    print("📋 Detailed results and recommendations saved to comprehensive_test_results.json")
    print("🚀 Ready for next iteration of improvements!")
    print("=" * 80)

if __name__ == "__main__":
    main()
