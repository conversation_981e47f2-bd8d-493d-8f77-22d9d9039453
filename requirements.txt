# Enhanced requirements for hybrid bias detection
# Add these to your existing requirements

# Core dependencies
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# NLP dependencies (required)
nltk>=3.6
spacy>=3.4.0

# ML dependencies (optional but recommended for best performance)
transformers>=4.20.0
torch>=1.12.0

# For data processing
dataclasses-json>=0.5.7

# Installation commands:
# pip install numpy pandas scikit-learn nltk spacy
# python -m spacy download en_core_web_sm
#
# For ML features (optional):
# pip install transformers torch
#
# Note: If you don't install transformers, the system will automatically
# fall back to rule-based detection only
