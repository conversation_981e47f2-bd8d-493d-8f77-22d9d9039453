"""
Hybrid Bias Detection for Indian Courtroom Proceedings
Combines rule-based, ML-based, and contextual approaches for maximum accuracy
Integrates with existing ASR -> Diarization -> Summarization pipeline
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
import json
import logging
from collections import defaultdict, Counter
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('tokenizers/punkt_tab')
except LookupError:
    nltk.download('punkt_tab')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

@dataclass
class BiasDetection:
    bias_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    instances: List[str]
    confidence_score: float  # 0.0 to 1.0
    detection_method: str  # 'rule_based', 'ml_based', 'contextual', 'hybrid'
    context: str = ""
    speaker: str = None
    timestamp: str = None
    linguistic_markers: List[str] = None

    def __post_init__(self):
        if self.linguistic_markers is None:
            self.linguistic_markers = []

class HybridIndianCourtroomBiasDetector:
    def __init__(self, enable_ml_models=True):
        self.enable_ml_models = enable_ml_models
        self.logger = logging.getLogger(__name__)

        # Initialize ML components if enabled
        if self.enable_ml_models:
            try:
                from transformers import pipeline
                self.sentiment_analyzer = pipeline("sentiment-analysis", device=-1)
                self.emotion_analyzer = pipeline("text-classification",
                                               model="j-hartmann/emotion-english-distilroberta-base",
                                               device=-1)
                self.ml_available = True
            except ImportError:
                self.logger.warning("Transformers not available. Using rule-based approach only.")
                self.ml_available = False
        else:
            self.ml_available = False

        # Initialize stopwords
        try:
            self.stop_words = set(stopwords.words('english'))
        except:
            self.stop_words = set()

        # Enhanced Indian context-specific bias patterns with confidence weights
        self.bias_patterns = {
            'gender_bias': {
                'keywords': {
                    # High confidence indicators
                    'ladki': 0.8, 'ladka': 0.8, 'aurat': 0.9, 'mard': 0.9,
                    'stree': 0.9, 'purush': 0.9, 'beti': 0.7, 'beta': 0.7,
                    'bahu': 0.8, 'damad': 0.8, 'patni': 0.7, 'pati': 0.7,
                    'emotional': 0.6, 'hysterical': 0.9, 'hormonal': 0.8,
                    'feminine': 0.7, 'masculine': 0.7, 'manly': 0.8,
                    # Context-dependent (very low confidence - need strong context)
                    'she': 0.1, 'he': 0.1, 'woman': 0.2, 'man': 0.2,
                    'female': 0.2, 'male': 0.2, 'girl': 0.2, 'boy': 0.2
                },
                'phrases': {
                    'women are emotional': 0.95, 'typical woman behavior': 0.9,
                    'men don\'t cry': 0.85, 'she is just a woman': 0.95,
                    'ladkiyon ka kaam': 0.9, 'ghar ka kaam': 0.8,
                    'women can\'t handle': 0.9, 'men are stronger': 0.8,
                    'typical female': 0.85, 'typical male': 0.85,
                    'women drivers': 0.9, 'like a girl': 0.8
                },
                'contextual_patterns': [
                    r'women? (?:are|can\'t|cannot|don\'t|always|never)',
                    r'men? (?:are|can\'t|cannot|don\'t|always|never)',
                    r'(?:all|most|typical) (?:women|men|girls|boys)'
                ]
            },
            'caste_bias': {
                'keywords': {
                    'brahmin': 0.8, 'kshatriya': 0.8, 'vaishya': 0.8, 'shudra': 0.9,
                    'dalit': 0.9, 'scheduled caste': 0.9, 'scheduled tribe': 0.9,
                    'obc': 0.8, 'upper caste': 0.9, 'lower caste': 0.95,
                    'untouchable': 0.95, 'harijan': 0.9, 'adivasi': 0.8,
                    'sc/st': 0.9, 'backward': 0.7, 'forward': 0.7,
                    'reservation': 0.6, 'quota': 0.6, 'merit': 0.5
                },
                'phrases': {
                    'people like them': 0.8, 'their community': 0.7, 'their kind': 0.9,
                    'typical of their caste': 0.95, 'born criminal': 0.95,
                    'caste people': 0.9, 'these people': 0.7,
                    'reservation candidate': 0.8, 'quota system': 0.7,
                    'merit vs reservation': 0.8, 'undeserving': 0.8
                },
                'contextual_patterns': [
                    r'(?:all|most|typical) (?:dalits?|brahmins?|sc|st)',
                    r'(?:upper|lower) caste (?:people|person|mentality)',
                    r'reservation (?:candidate|student|person)'
                ]
            },
            'religious_bias': {
                'keywords': {
                    'hindu': 0.6, 'muslim': 0.6, 'christian': 0.6, 'sikh': 0.6,
                    'buddhist': 0.6, 'jain': 0.6, 'kafir': 0.95, 'jihadi': 0.95,
                    'terrorist': 0.9, 'communal': 0.8, 'minority': 0.7,
                    'majority': 0.7, 'secular': 0.5, 'fundamentalist': 0.9,
                    'islamist': 0.9, 'hindutva': 0.8, 'sanghi': 0.9
                },
                'phrases': {
                    'these people': 0.7, 'their religion teaches': 0.8,
                    'typical muslim': 0.9, 'hindu rashtra': 0.8,
                    'love jihad': 0.95, 'forced conversion': 0.9,
                    'anti-national': 0.8, 'go to pakistan': 0.95,
                    'religious fanatic': 0.9, 'communal minded': 0.8
                },
                'contextual_patterns': [
                    r'(?:all|most|typical) (?:muslims?|hindus?|christians?)',
                    r'(?:muslim|hindu|christian) (?:mentality|behavior|nature)'
                ]
            },
            'economic_bias': {
                'keywords': {
                    'poor': 0.6, 'rich': 0.6, 'gareeb': 0.7, 'ameer': 0.7,
                    'slum': 0.8, 'jhuggi': 0.8, 'bpl': 0.8, 'below poverty line': 0.8,
                    'elite': 0.7, 'privileged': 0.7, 'underprivileged': 0.7,
                    'backward': 0.7, 'wealthy': 0.6, 'poverty': 0.6
                },
                'phrases': {
                    'poor people lie': 0.95, 'rich can buy justice': 0.9,
                    'money talks': 0.8, 'typical poor mentality': 0.9,
                    'slum mentality': 0.9, 'born in poverty': 0.8,
                    'privileged background': 0.7, 'economic status': 0.6
                },
                'contextual_patterns': [
                    r'(?:all|most|typical) (?:poor|rich) (?:people|person)',
                    r'(?:poor|rich) (?:mentality|behavior|nature)'
                ]
            },
            'regional_bias': {
                'keywords': {
                    'north indian': 0.7, 'south indian': 0.7, 'bihari': 0.8,
                    'bengali': 0.7, 'punjabi': 0.7, 'gujarati': 0.7,
                    'marathi': 0.7, 'tamil': 0.7, 'outsider': 0.8,
                    'local': 0.6, 'migrant': 0.7, 'refugee': 0.8,
                    'up wala': 0.8, 'madrasi': 0.9, 'bhaiya': 0.8
                },
                'phrases': {
                    'these outsiders': 0.9, 'not from here': 0.8,
                    'typical bihari': 0.9, 'south indian mentality': 0.9,
                    'north indian arrogance': 0.9, 'go back': 0.9,
                    'local people': 0.7, 'regional bias': 0.8
                },
                'contextual_patterns': [
                    r'(?:all|most|typical) (?:biharis?|bengalis?|tamils?)',
                    r'(?:north|south) indian (?:mentality|behavior|nature)'
                ]
            },
            'language_bias': {
                'keywords': {
                    'english speaking': 0.7, 'vernacular': 0.7, 'mother tongue': 0.6,
                    'hindi imposition': 0.8, 'local language': 0.6, 'broken english': 0.8,
                    'accent': 0.7, 'pronunciation': 0.7, 'convent educated': 0.7
                },
                'phrases': {
                    'can\'t speak proper english': 0.9, 'village mentality': 0.8,
                    'uneducated speech': 0.9, 'hindi speaking': 0.7,
                    'english medium': 0.7, 'vernacular medium': 0.7
                },
                'contextual_patterns': [
                    r'(?:can\'t|cannot) speak (?:proper|good) english',
                    r'(?:broken|poor) english'
                ]
            }
        }

        # Enhanced severity indicators with confidence weights
        self.severity_indicators = {
            'critical': {
                'patterns': ['always', 'never', 'all of them', 'none of them', 'every single',
                           'without exception', 'inherently', 'genetically', 'by nature'],
                'weight': 1.0
            },
            'high': {
                'patterns': ['typical', 'born', 'natural', 'usually', 'generally',
                           'most of them', 'majority of', 'commonly', 'tend to'],
                'weight': 0.8
            },
            'medium': {
                'patterns': ['often', 'frequently', 'many', 'several', 'quite a few'],
                'weight': 0.6
            },
            'low': {
                'patterns': ['sometimes', 'occasionally', 'few', 'some', 'might'],
                'weight': 0.4
            }
        }

        # Contextual amplifiers that increase bias severity
        self.bias_amplifiers = [
            'obviously', 'clearly', 'definitely', 'certainly', 'undoubtedly',
            'of course', 'naturally', 'inevitably', 'predictably'
        ]

        # Contextual mitigators that reduce bias severity
        self.bias_mitigators = [
            'perhaps', 'maybe', 'possibly', 'might', 'could be',
            'seems like', 'appears to', 'allegedly', 'reportedly'
        ]

        # Initialize TF-IDF vectorizer for semantic analysis
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 3)
        )

    def detect_bias_in_text(self, text: str, speaker: str = None, timestamp: str = None) -> List[BiasDetection]:
        """
        Hybrid bias detection combining multiple approaches
        """
        detections = []

        # Method 1: Rule-based detection
        rule_based_detections = self._rule_based_detection(text, speaker, timestamp)
        detections.extend(rule_based_detections)

        # Method 2: ML-based detection (if available)
        if self.ml_available:
            ml_detections = self._ml_based_detection(text, speaker, timestamp)
            detections.extend(ml_detections)

        # Method 3: Contextual pattern detection
        contextual_detections = self._contextual_pattern_detection(text, speaker, timestamp)
        detections.extend(contextual_detections)

        # Method 4: Semantic similarity detection
        semantic_detections = self._semantic_similarity_detection(text, speaker, timestamp)
        detections.extend(semantic_detections)

        # Merge and deduplicate similar detections
        merged_detections = self._merge_detections(detections)

        # Apply confidence boosting for multiple detection methods
        final_detections = self._boost_confidence_for_consensus(merged_detections)

        return final_detections

    def _rule_based_detection(self, text: str, speaker: str, timestamp: str) -> List[BiasDetection]:
        """Enhanced rule-based detection with confidence scoring"""
        detections = []
        text_lower = text.lower()
        sentences = sent_tokenize(text)

        for bias_type, patterns in self.bias_patterns.items():
            instances = []
            confidence_scores = []
            linguistic_markers = []

            # Check keyword matches with confidence weights
            for keyword, confidence in patterns['keywords'].items():
                if keyword.lower() in text_lower:
                    for sentence in sentences:
                        if keyword.lower() in sentence.lower():
                            # Apply context filtering for low-confidence keywords
                            if confidence < 0.3 and not self._has_bias_context(sentence, bias_type):
                                continue
                            instances.append(sentence.strip())
                            confidence_scores.append(confidence)
                            linguistic_markers.append(f"keyword: {keyword}")

            # Check phrase matches with confidence weights
            for phrase, confidence in patterns['phrases'].items():
                if phrase.lower() in text_lower:
                    for sentence in sentences:
                        if phrase.lower() in sentence.lower():
                            instances.append(sentence.strip())
                            confidence_scores.append(confidence)
                            linguistic_markers.append(f"phrase: {phrase}")

            if instances:
                # Calculate average confidence
                avg_confidence = np.mean(confidence_scores) if confidence_scores else 0.5

                # Determine severity with context
                severity = self._determine_severity_enhanced(text_lower, avg_confidence)

                detections.append(BiasDetection(
                    bias_type=bias_type,
                    severity=severity,
                    instances=list(set(instances)),
                    confidence_score=avg_confidence,
                    detection_method='rule_based',
                    context=self._extract_context(text, instances[0] if instances else ""),
                    speaker=speaker,
                    timestamp=timestamp,
                    linguistic_markers=list(set(linguistic_markers))
                ))

        return detections

    def _has_bias_context(self, sentence: str, bias_type: str) -> bool:
        """Check if a sentence has sufficient context to indicate bias"""
        sentence_lower = sentence.lower()

        # Context indicators that suggest bias
        bias_context_indicators = [
            'always', 'never', 'all', 'every', 'typical', 'naturally', 'born',
            'cannot', 'can\'t', 'should not', 'shouldn\'t', 'too', 'very',
            'obviously', 'clearly', 'definitely', 'usually', 'generally'
        ]

        # Check for bias context indicators
        has_context = any(indicator in sentence_lower for indicator in bias_context_indicators)

        # Additional context checks for specific bias types
        if bias_type == 'gender_bias':
            gender_context = any(word in sentence_lower for word in [
                'emotional', 'rational', 'decision', 'leadership', 'weak', 'strong',
                'hysterical', 'calm', 'better', 'worse', 'superior', 'inferior'
            ])
            has_context = has_context or gender_context

        return has_context

    def _contextual_pattern_detection(self, text: str, speaker: str, timestamp: str) -> List[BiasDetection]:
        """Detect bias using regex patterns and contextual analysis"""
        detections = []
        text_lower = text.lower()

        for bias_type, patterns in self.bias_patterns.items():
            if 'contextual_patterns' not in patterns:
                continue

            instances = []
            confidence_scores = []

            for pattern in patterns['contextual_patterns']:
                matches = re.finditer(pattern, text_lower, re.IGNORECASE)
                for match in matches:
                    # Extract the full sentence containing the match
                    start = max(0, text.rfind('.', 0, match.start()) + 1)
                    end = text.find('.', match.end())
                    if end == -1:
                        end = len(text)

                    sentence = text[start:end].strip()
                    if sentence:
                        instances.append(sentence)
                        confidence_scores.append(0.8)  # High confidence for pattern matches

            if instances:
                avg_confidence = np.mean(confidence_scores)
                severity = self._determine_severity_enhanced(text_lower, avg_confidence)

                detections.append(BiasDetection(
                    bias_type=bias_type,
                    severity=severity,
                    instances=list(set(instances)),
                    confidence_score=avg_confidence,
                    detection_method='contextual',
                    context=self._extract_context(text, instances[0]),
                    speaker=speaker,
                    timestamp=timestamp,
                    linguistic_markers=[f"pattern: {p}" for p in patterns['contextual_patterns']]
                ))

        return detections

    def _ml_based_detection(self, text: str, speaker: str, timestamp: str) -> List[BiasDetection]:
        """ML-based bias detection using sentiment and emotion analysis"""
        if not self.ml_available:
            return []

        detections = []

        try:
            # Sentiment analysis
            sentiment_result = self.sentiment_analyzer(text)[0]

            # Emotion analysis
            emotion_result = self.emotion_analyzer(text)[0]

            # Check for negative sentiment combined with identity-related terms
            if sentiment_result['label'] == 'NEGATIVE' and sentiment_result['score'] > 0.8:
                # Check if text contains identity markers
                identity_terms = self._extract_identity_terms(text)

                if identity_terms:
                    # Determine bias type based on identity terms
                    bias_type = self._classify_bias_type_from_terms(identity_terms)

                    if bias_type:
                        detections.append(BiasDetection(
                            bias_type=bias_type,
                            severity=self._map_ml_confidence_to_severity(sentiment_result['score']),
                            instances=[text],
                            confidence_score=sentiment_result['score'],
                            detection_method='ml_based',
                            context=f"Sentiment: {sentiment_result['label']}, Emotion: {emotion_result['label']}",
                            speaker=speaker,
                            timestamp=timestamp,
                            linguistic_markers=[f"ml_sentiment: {sentiment_result['label']}",
                                              f"ml_emotion: {emotion_result['label']}"]
                        ))

        except Exception as e:
            self.logger.warning(f"ML-based detection failed: {e}")

        return detections

    def _semantic_similarity_detection(self, text: str, speaker: str, timestamp: str) -> List[BiasDetection]:
        """Detect bias using semantic similarity to known biased statements"""
        detections = []

        # Known biased statement templates for comparison
        bias_templates = {
            'gender_bias': [
                "women are too emotional for important decisions",
                "men are naturally better leaders",
                "typical female behavior in stressful situations"
            ],
            'caste_bias': [
                "people from lower castes are naturally dishonest",
                "upper caste people are more intelligent",
                "typical behavior of their community"
            ],
            'religious_bias': [
                "people of that religion are violent by nature",
                "their religious beliefs make them untrustworthy",
                "typical behavior of their religious community"
            ]
        }

        try:
            # Vectorize input text
            all_texts = [text]
            for bias_type, templates in bias_templates.items():
                all_texts.extend(templates)

            tfidf_matrix = self.vectorizer.fit_transform(all_texts)
            similarities = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:]).flatten()

            # Check for high similarity with bias templates
            template_idx = 0
            for bias_type, templates in bias_templates.items():
                for template in templates:
                    similarity_score = similarities[template_idx]
                    if similarity_score > 0.6:  # Threshold for semantic similarity
                        detections.append(BiasDetection(
                            bias_type=bias_type,
                            severity=self._map_similarity_to_severity(similarity_score),
                            instances=[text],
                            confidence_score=similarity_score,
                            detection_method='semantic',
                            context=f"Similar to: {template}",
                            speaker=speaker,
                            timestamp=timestamp,
                            linguistic_markers=[f"semantic_similarity: {similarity_score:.2f}"]
                        ))
                    template_idx += 1

        except Exception as e:
            self.logger.warning(f"Semantic similarity detection failed: {e}")

        return detections

    def detect_bias_in_diarized_text(self, diarized_segments: List[Dict]) -> Dict:
        """
        Process diarized text segments for bias detection
        Expected format: [{'speaker': 'Speaker_1', 'text': '...', 'timestamp': '00:01:30'}]
        """
        all_detections = []
        speaker_bias_summary = {}
        
        for segment in diarized_segments:
            speaker = segment.get('speaker', 'Unknown')
            text = segment.get('text', '')
            timestamp = segment.get('timestamp', '')
            
            # Detect bias in this segment
            detections = self.detect_bias_in_text(text, speaker, timestamp)
            all_detections.extend(detections)
            
            # Track bias by speaker
            if detections:
                if speaker not in speaker_bias_summary:
                    speaker_bias_summary[speaker] = {
                        'total_instances': 0,
                        'bias_types': {},
                        'severity_distribution': {'low': 0, 'medium': 0, 'high': 0}
                    }
                
                for detection in detections:
                    speaker_bias_summary[speaker]['total_instances'] += len(detection.instances)
                    
                    if detection.bias_type not in speaker_bias_summary[speaker]['bias_types']:
                        speaker_bias_summary[speaker]['bias_types'][detection.bias_type] = 0
                    speaker_bias_summary[speaker]['bias_types'][detection.bias_type] += len(detection.instances)
                    
                    speaker_bias_summary[speaker]['severity_distribution'][detection.severity] += len(detection.instances)
        
        return {
            'detections': all_detections,
            'speaker_summary': speaker_bias_summary,
            'overall_summary': self._generate_overall_summary(all_detections),
            'confidence_metrics': self._calculate_confidence_metrics(all_detections)
        }

    def _merge_detections(self, detections: List[BiasDetection]) -> List[BiasDetection]:
        """Merge similar detections from different methods"""
        merged = {}

        for detection in detections:
            key = f"{detection.bias_type}_{detection.speaker}"

            if key not in merged:
                merged[key] = detection
            else:
                # Merge instances and update confidence
                existing = merged[key]
                existing.instances.extend(detection.instances)
                existing.instances = list(set(existing.instances))  # Remove duplicates

                # Combine confidence scores (weighted average)
                existing.confidence_score = (existing.confidence_score + detection.confidence_score) / 2

                # Combine detection methods
                if detection.detection_method not in existing.detection_method:
                    existing.detection_method = f"{existing.detection_method}+{detection.detection_method}"

                # Combine linguistic markers
                existing.linguistic_markers.extend(detection.linguistic_markers)
                existing.linguistic_markers = list(set(existing.linguistic_markers))

        return list(merged.values())

    def _boost_confidence_for_consensus(self, detections: List[BiasDetection]) -> List[BiasDetection]:
        """Boost confidence when multiple methods agree"""
        for detection in detections:
            method_count = len(detection.detection_method.split('+'))
            if method_count > 1:
                # Boost confidence for consensus
                boost_factor = 1 + (method_count - 1) * 0.1
                detection.confidence_score = min(1.0, detection.confidence_score * boost_factor)

                # Update severity based on boosted confidence
                if detection.confidence_score > 0.9:
                    detection.severity = 'critical'
                elif detection.confidence_score > 0.7:
                    detection.severity = 'high'

        return detections

    def _determine_severity_enhanced(self, text: str, base_confidence: float) -> str:
        """Enhanced severity determination with confidence and context"""
        severity_score = base_confidence

        # Check for severity indicators
        for severity_level, data in self.severity_indicators.items():
            for pattern in data['patterns']:
                if pattern in text:
                    severity_score *= data['weight']
                    break

        # Check for amplifiers and mitigators
        for amplifier in self.bias_amplifiers:
            if amplifier in text:
                severity_score *= 1.2
                break

        for mitigator in self.bias_mitigators:
            if mitigator in text:
                severity_score *= 0.8
                break

        # Map score to severity level
        if severity_score >= 0.9:
            return 'critical'
        elif severity_score >= 0.7:
            return 'high'
        elif severity_score >= 0.5:
            return 'medium'
        else:
            return 'low'

    def _extract_context(self, full_text: str, target_sentence: str, window_size: int = 50) -> str:
        """Extract context around the biased statement"""
        if not target_sentence or target_sentence not in full_text:
            return ""

        start_idx = full_text.find(target_sentence)
        context_start = max(0, start_idx - window_size)
        context_end = min(len(full_text), start_idx + len(target_sentence) + window_size)

        return full_text[context_start:context_end].strip()

    def _extract_identity_terms(self, text: str) -> List[str]:
        """Extract identity-related terms from text"""
        identity_terms = []
        text_lower = text.lower()

        for bias_type, patterns in self.bias_patterns.items():
            for keyword in patterns['keywords']:
                if keyword.lower() in text_lower:
                    identity_terms.append(keyword)

        return identity_terms

    def _classify_bias_type_from_terms(self, identity_terms: List[str]) -> str:
        """Classify bias type based on identity terms found"""
        bias_scores = defaultdict(int)

        for term in identity_terms:
            for bias_type, patterns in self.bias_patterns.items():
                if term.lower() in [k.lower() for k in patterns['keywords']]:
                    bias_scores[bias_type] += 1

        return max(bias_scores, key=bias_scores.get) if bias_scores else None

    def _map_ml_confidence_to_severity(self, confidence: float) -> str:
        """Map ML model confidence to severity level"""
        if confidence >= 0.95:
            return 'critical'
        elif confidence >= 0.8:
            return 'high'
        elif confidence >= 0.6:
            return 'medium'
        else:
            return 'low'

    def _map_similarity_to_severity(self, similarity: float) -> str:
        """Map semantic similarity score to severity level"""
        if similarity >= 0.9:
            return 'critical'
        elif similarity >= 0.75:
            return 'high'
        elif similarity >= 0.6:
            return 'medium'
        else:
            return 'low'

    def _calculate_confidence_metrics(self, detections: List[BiasDetection]) -> Dict:
        """Calculate overall confidence metrics"""
        if not detections:
            return {'average_confidence': 0.0, 'high_confidence_count': 0, 'method_distribution': {}}

        confidences = [d.confidence_score for d in detections]
        methods = [d.detection_method for d in detections]

        return {
            'average_confidence': np.mean(confidences),
            'high_confidence_count': sum(1 for c in confidences if c > 0.8),
            'method_distribution': dict(Counter(methods)),
            'confidence_distribution': {
                'high': sum(1 for c in confidences if c > 0.8),
                'medium': sum(1 for c in confidences if 0.5 < c <= 0.8),
                'low': sum(1 for c in confidences if c <= 0.5)
            }
        }

    def _generate_overall_summary(self, detections: List[BiasDetection]) -> Dict:
        """Generate overall bias summary"""
        if not detections:
            return {'total_bias_instances': 0, 'bias_types': {}, 'severity_distribution': {}}
        
        bias_types = {}
        severity_dist = {'low': 0, 'medium': 0, 'high': 0}
        total_instances = 0
        
        for detection in detections:
            if detection.bias_type not in bias_types:
                bias_types[detection.bias_type] = 0
            bias_types[detection.bias_type] += len(detection.instances)
            severity_dist[detection.severity] += len(detection.instances)
            total_instances += len(detection.instances)
        
        return {
            'total_bias_instances': total_instances,
            'bias_types': bias_types,
            'severity_distribution': severity_dist,
            'bias_score': self._calculate_bias_score(severity_dist, total_instances)
        }

    def _calculate_bias_score(self, severity_dist: Dict, total: int) -> float:
        """Calculate a bias score from 0-10 (10 being most biased)"""
        if total == 0:
            return 0.0
        
        weighted_score = (
            severity_dist['low'] * 1 + 
            severity_dist['medium'] * 3 + 
            severity_dist['high'] * 5
        )
        
        # Normalize to 0-10 scale
        max_possible = total * 5
        return min(10.0, (weighted_score / max_possible) * 10)

    def generate_bias_report(self, bias_results: Dict) -> str:
        """Generate a human-readable bias report"""
        if bias_results['overall_summary']['total_bias_instances'] == 0:
            return "No bias detected in the courtroom proceedings."
        
        report = []
        report.append("=== COURTROOM BIAS DETECTION REPORT ===\n")
        
        overall = bias_results['overall_summary']
        report.append(f"Overall Bias Score: {overall['bias_score']:.1f}/10")
        report.append(f"Total Bias Instances: {overall['total_bias_instances']}")
        
        report.append("\n--- Bias Types Detected ---")
        for bias_type, count in overall['bias_types'].items():
            report.append(f"• {bias_type.replace('_', ' ').title()}: {count} instances")
        
        report.append("\n--- Severity Distribution ---")
        for severity, count in overall['severity_distribution'].items():
            report.append(f"• {severity.title()}: {count} instances")
        
        if bias_results['speaker_summary']:
            report.append("\n--- Speaker-wise Analysis ---")
            for speaker, data in bias_results['speaker_summary'].items():
                report.append(f"\n{speaker}:")
                report.append(f"  Total instances: {data['total_instances']}")
                report.append(f"  Main bias types: {', '.join(data['bias_types'].keys())}")
        
        return "\n".join(report)

    def get_detection_summary(self, bias_results: Dict) -> Dict:
        """Get a comprehensive summary of detection results"""
        detections = bias_results.get('detections', [])
        confidence_metrics = bias_results.get('confidence_metrics', {})

        if not detections:
            return {
                'total_detections': 0,
                'bias_free': True,
                'summary': "No bias detected in the proceedings."
            }

        # Analyze detection patterns
        bias_by_type = defaultdict(list)
        bias_by_speaker = defaultdict(list)
        methods_effectiveness = defaultdict(int)

        for detection in detections:
            bias_by_type[detection.bias_type].append(detection)
            bias_by_speaker[detection.speaker or 'Unknown'].append(detection)
            for method in detection.detection_method.split('+'):
                methods_effectiveness[method] += 1

        # Calculate risk assessment
        high_risk_speakers = [
            speaker for speaker, dets in bias_by_speaker.items()
            if len(dets) > 2 or any(d.severity in ['high', 'critical'] for d in dets)
        ]

        return {
            'total_detections': len(detections),
            'bias_free': False,
            'unique_bias_types': len(bias_by_type),
            'affected_speakers': len(bias_by_speaker),
            'high_risk_speakers': high_risk_speakers,
            'most_common_bias': max(bias_by_type.keys(), key=lambda x: len(bias_by_type[x])),
            'detection_methods_used': list(methods_effectiveness.keys()),
            'most_effective_method': max(methods_effectiveness.keys(), key=methods_effectiveness.get),
            'average_confidence': confidence_metrics.get('average_confidence', 0.0),
            'summary': f"Detected {len(detections)} bias instances across {len(bias_by_type)} categories"
        }

# Enhanced integration functions for your existing pipeline
def integrate_bias_detection(diarized_text_output, enable_ml=True):
    """
    Enhanced function to integrate hybrid bias detection with your existing pipeline
    Call this after your diarization step and before/after summarization

    Args:
        diarized_text_output: List of diarized segments
        enable_ml: Whether to enable ML-based detection (requires transformers)
    """
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=enable_ml)
    bias_results = detector.detect_bias_in_diarized_text(diarized_text_output)
    bias_report = detector.generate_bias_report(bias_results)

    return {
        'bias_results': bias_results,
        'bias_report': bias_report,
        'confidence_metrics': bias_results.get('confidence_metrics', {}),
        'detection_summary': detector.get_detection_summary(bias_results)
    }

def quick_bias_check_hybrid(text: str, speaker: str = "Unknown", enable_ml: bool = True) -> Dict:
    """
    Quick hybrid bias check for a single text segment
    """
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=enable_ml)
    detections = detector.detect_bias_in_text(text, speaker)

    if not detections:
        return {
            'bias_detected': False,
            'message': f"No bias detected in {speaker}'s statement.",
            'confidence': 0.0
        }

    # Aggregate results
    total_confidence = np.mean([d.confidence_score for d in detections])
    bias_types = [d.bias_type for d in detections]
    methods_used = list(set([d.detection_method for d in detections]))

    return {
        'bias_detected': True,
        'bias_types': bias_types,
        'overall_confidence': total_confidence,
        'detection_methods': methods_used,
        'detections': detections,
        'summary': f"Detected {len(detections)} bias instances with {total_confidence:.2f} confidence"
    }

# Example usage and testing
if __name__ == "__main__":
    print("=== HYBRID BIAS DETECTION DEMO ===\n")

    # Example diarized text with various bias types
    sample_diarized_text = [
        {
            'speaker': 'Judge',
            'text': 'The defendant, being from a poor background and lower caste, is naturally inclined towards criminal behavior.',
            'timestamp': '00:01:30'
        },
        {
            'speaker': 'Lawyer_Defense',
            'text': 'Your honor, my client is innocent. The evidence is circumstantial.',
            'timestamp': '00:02:15'
        },
        {
            'speaker': 'Lawyer_Prosecution',
            'text': 'Women are typically emotional and unreliable witnesses. This Muslim defendant clearly shows typical behavior of his community.',
            'timestamp': '00:03:00'
        },
        {
            'speaker': 'Witness',
            'text': 'I saw what happened clearly. The accused seemed like a decent person.',
            'timestamp': '00:04:30'
        }
    ]

    # Test both ML-enabled and rule-based approaches
    print("Testing with ML models enabled:")
    print("-" * 40)
    result_ml = integrate_bias_detection(sample_diarized_text, enable_ml=True)
    print(result_ml['bias_report'])
    print(f"\nDetection Summary: {result_ml['detection_summary']['summary']}")
    print(f"Average Confidence: {result_ml['confidence_metrics'].get('average_confidence', 0):.2f}")

    print("\n" + "="*60)
    print("Testing with rule-based approach only:")
    print("-" * 40)
    result_rules = integrate_bias_detection(sample_diarized_text, enable_ml=False)
    print(result_rules['bias_report'])

    print("\n" + "="*60)
    print("QUICK BIAS CHECK EXAMPLES:")
    print("-" * 40)

    # Test individual statements
    test_statements = [
        ("Judge", "All people from that community are naturally dishonest and prone to violence."),
        ("Lawyer", "Women are obviously too emotional to make rational decisions in legal matters."),
        ("Prosecutor", "The defendant, being a typical Bihari migrant, shows the expected criminal tendencies."),
        ("Witness", "I observed the incident clearly and can provide accurate testimony."),
    ]

    for speaker, statement in test_statements:
        print(f"\nTesting: {speaker}")
        print(f"Statement: '{statement[:60]}...'")
        result = quick_bias_check_hybrid(statement, speaker, enable_ml=False)

        if result['bias_detected']:
            print(f"✗ BIAS DETECTED: {', '.join(result['bias_types'])}")
            print(f"  Confidence: {result['overall_confidence']:.2f}")
            print(f"  Methods: {', '.join(result['detection_methods'])}")
        else:
            print("✓ No bias detected")
        print("-" * 40)
