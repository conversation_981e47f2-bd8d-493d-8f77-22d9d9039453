"""
Simple Bias Detection for Indian Courtroom Proceedings
Integrates with existing ASR -> Diarization -> Summarization pipeline
"""

import re
from typing import Dict, List, Tuple
from dataclasses import dataclass
import json

@dataclass
class BiasDetection:
    bias_type: str
    severity: str  # 'low', 'medium', 'high'
    instances: List[str]
    speaker: str = None
    timestamp: str = None

class IndianCourtroomBiasDetector:
    def __init__(self):
        # Indian context-specific bias patterns
        self.bias_patterns = {
            'gender_bias': {
                'keywords': [
                    'ladki', 'ladka', 'aurat', 'mard', 'stree', 'purush',
                    'beti', 'beta', 'bahu', 'damad', 'patni', 'pati',
                    'ma', 'baap', 'sasur', 'saas', 'emotional', 'hysterical'
                ],
                'phrases': [
                    'women are emotional', 'typical woman behavior', 
                    'men don\'t cry', 'she is just a woman',
                    'ladkiyon ka kaam', 'ghar ka kaam'
                ]
            },
            'caste_bias': {
                'keywords': [
                    'brahmin', 'kshatriya', 'vaishya', 'shudra', 'dalit',
                    'scheduled caste', 'scheduled tribe', 'obc', 'upper caste',
                    'lower caste', 'untouchable', 'harijan', 'adivasi'
                ],
                'phrases': [
                    'people like them', 'their community', 'their kind',
                    'typical of their caste', 'born criminal'
                ]
            },
            'religious_bias': {
                'keywords': [
                    'hindu', 'muslim', 'christian', 'sikh', 'buddhist', 'jain',
                    'kafir', 'jihadi', 'terrorist', 'communal', 'minority',
                    'majority', 'secular', 'fundamentalist'
                ],
                'phrases': [
                    'these people', 'their religion teaches', 'typical muslim',
                    'hindu rashtra', 'love jihad', 'forced conversion'
                ]
            },
            'economic_bias': {
                'keywords': [
                    'poor', 'rich', 'gareeb', 'ameer', 'slum', 'jhuggi',
                    'bpl', 'below poverty line', 'elite', 'privileged',
                    'underprivileged', 'backward'
                ],
                'phrases': [
                    'poor people lie', 'rich can buy justice', 
                    'money talks', 'typical poor mentality'
                ]
            },
            'regional_bias': {
                'keywords': [
                    'north indian', 'south indian', 'bihari', 'bengali',
                    'punjabi', 'gujarati', 'marathi', 'tamil', 'outsider',
                    'local', 'migrant', 'refugee'
                ],
                'phrases': [
                    'these outsiders', 'not from here', 'typical bihari',
                    'south indian mentality', 'north indian arrogance'
                ]
            },
            'language_bias': {
                'keywords': [
                    'english speaking', 'vernacular', 'mother tongue',
                    'hindi imposition', 'local language', 'broken english',
                    'accent', 'pronunciation'
                ],
                'phrases': [
                    'can\'t speak proper english', 'village mentality',
                    'uneducated speech', 'hindi speaking'
                ]
            }
        }
        
        # Severity indicators
        self.severity_indicators = {
            'high': [
                'always', 'never', 'all of them', 'none of them',
                'typical', 'born', 'natural', 'genetic', 'inherent'
            ],
            'medium': [
                'usually', 'often', 'most', 'generally', 'commonly'
            ],
            'low': [
                'sometimes', 'occasionally', 'few', 'some'
            ]
        }

    def detect_bias_in_text(self, text: str, speaker: str = None, timestamp: str = None) -> List[BiasDetection]:
        """
        Detect bias in a single text segment
        """
        detections = []
        text_lower = text.lower()
        
        for bias_type, patterns in self.bias_patterns.items():
            instances = []
            
            # Check for keyword matches
            for keyword in patterns['keywords']:
                if keyword.lower() in text_lower:
                    # Find the sentence containing the keyword
                    sentences = re.split(r'[.!?]+', text)
                    for sentence in sentences:
                        if keyword.lower() in sentence.lower():
                            instances.append(sentence.strip())
            
            # Check for phrase matches
            for phrase in patterns['phrases']:
                if phrase.lower() in text_lower:
                    # Find the sentence containing the phrase
                    sentences = re.split(r'[.!?]+', text)
                    for sentence in sentences:
                        if phrase.lower() in sentence.lower():
                            instances.append(sentence.strip())
            
            if instances:
                # Determine severity
                severity = self._determine_severity(text_lower)
                
                detections.append(BiasDetection(
                    bias_type=bias_type,
                    severity=severity,
                    instances=list(set(instances)),  # Remove duplicates
                    speaker=speaker,
                    timestamp=timestamp
                ))
        
        return detections

    def detect_bias_in_diarized_text(self, diarized_segments: List[Dict]) -> Dict:
        """
        Process diarized text segments for bias detection
        Expected format: [{'speaker': 'Speaker_1', 'text': '...', 'timestamp': '00:01:30'}]
        """
        all_detections = []
        speaker_bias_summary = {}
        
        for segment in diarized_segments:
            speaker = segment.get('speaker', 'Unknown')
            text = segment.get('text', '')
            timestamp = segment.get('timestamp', '')
            
            # Detect bias in this segment
            detections = self.detect_bias_in_text(text, speaker, timestamp)
            all_detections.extend(detections)
            
            # Track bias by speaker
            if detections:
                if speaker not in speaker_bias_summary:
                    speaker_bias_summary[speaker] = {
                        'total_instances': 0,
                        'bias_types': {},
                        'severity_distribution': {'low': 0, 'medium': 0, 'high': 0}
                    }
                
                for detection in detections:
                    speaker_bias_summary[speaker]['total_instances'] += len(detection.instances)
                    
                    if detection.bias_type not in speaker_bias_summary[speaker]['bias_types']:
                        speaker_bias_summary[speaker]['bias_types'][detection.bias_type] = 0
                    speaker_bias_summary[speaker]['bias_types'][detection.bias_type] += len(detection.instances)
                    
                    speaker_bias_summary[speaker]['severity_distribution'][detection.severity] += len(detection.instances)
        
        return {
            'detections': all_detections,
            'speaker_summary': speaker_bias_summary,
            'overall_summary': self._generate_overall_summary(all_detections)
        }

    def _determine_severity(self, text: str) -> str:
        """Determine the severity of bias based on language used"""
        for severity, indicators in self.severity_indicators.items():
            for indicator in indicators:
                if indicator in text:
                    return severity
        return 'low'  # default

    def _generate_overall_summary(self, detections: List[BiasDetection]) -> Dict:
        """Generate overall bias summary"""
        if not detections:
            return {'total_bias_instances': 0, 'bias_types': {}, 'severity_distribution': {}}
        
        bias_types = {}
        severity_dist = {'low': 0, 'medium': 0, 'high': 0}
        total_instances = 0
        
        for detection in detections:
            if detection.bias_type not in bias_types:
                bias_types[detection.bias_type] = 0
            bias_types[detection.bias_type] += len(detection.instances)
            severity_dist[detection.severity] += len(detection.instances)
            total_instances += len(detection.instances)
        
        return {
            'total_bias_instances': total_instances,
            'bias_types': bias_types,
            'severity_distribution': severity_dist,
            'bias_score': self._calculate_bias_score(severity_dist, total_instances)
        }

    def _calculate_bias_score(self, severity_dist: Dict, total: int) -> float:
        """Calculate a bias score from 0-10 (10 being most biased)"""
        if total == 0:
            return 0.0
        
        weighted_score = (
            severity_dist['low'] * 1 + 
            severity_dist['medium'] * 3 + 
            severity_dist['high'] * 5
        )
        
        # Normalize to 0-10 scale
        max_possible = total * 5
        return min(10.0, (weighted_score / max_possible) * 10)

    def generate_bias_report(self, bias_results: Dict) -> str:
        """Generate a human-readable bias report"""
        if bias_results['overall_summary']['total_bias_instances'] == 0:
            return "No bias detected in the courtroom proceedings."
        
        report = []
        report.append("=== COURTROOM BIAS DETECTION REPORT ===\n")
        
        overall = bias_results['overall_summary']
        report.append(f"Overall Bias Score: {overall['bias_score']:.1f}/10")
        report.append(f"Total Bias Instances: {overall['total_bias_instances']}")
        
        report.append("\n--- Bias Types Detected ---")
        for bias_type, count in overall['bias_types'].items():
            report.append(f"• {bias_type.replace('_', ' ').title()}: {count} instances")
        
        report.append("\n--- Severity Distribution ---")
        for severity, count in overall['severity_distribution'].items():
            report.append(f"• {severity.title()}: {count} instances")
        
        if bias_results['speaker_summary']:
            report.append("\n--- Speaker-wise Analysis ---")
            for speaker, data in bias_results['speaker_summary'].items():
                report.append(f"\n{speaker}:")
                report.append(f"  Total instances: {data['total_instances']}")
                report.append(f"  Main bias types: {', '.join(data['bias_types'].keys())}")
        
        return "\n".join(report)

# Simple integration function for your existing pipeline
def integrate_bias_detection(diarized_text_output):
    """
    Simple function to integrate with your existing pipeline
    Call this after your diarization step and before/after summarization
    """
    detector = IndianCourtroomBiasDetector()
    bias_results = detector.detect_bias_in_diarized_text(diarized_text_output)
    bias_report = detector.generate_bias_report(bias_results)
    
    return {
        'bias_results': bias_results,
        'bias_report': bias_report
    }

# Example usage
if __name__ == "__main__":
    # Example diarized text (replace with your actual output)
    sample_diarized_text = [
        {
            'speaker': 'Judge',
            'text': 'The defendant, being from a poor background, is likely to commit such crimes.',
            'timestamp': '00:01:30'
        },
        {
            'speaker': 'Lawyer_A',
            'text': 'Your honor, women are naturally emotional and cannot be trusted in such matters.',
            'timestamp': '00:02:15'
        }
    ]
    
    # Run bias detection
    result = integrate_bias_detection(sample_diarized_text)
    print(result['bias_report'])
