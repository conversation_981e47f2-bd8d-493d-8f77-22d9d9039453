"""
Test Advanced Bias Detection System - Next Iteration
Focus: Contextual Ambiguity, False Positive Reduction, Enhanced Performance
"""

from advanced_bias_detector import advanced_bias_detection, AdvancedBiasDetector
import json

def test_contextual_ambiguity_improvements():
    """Test improvements in contextual ambiguity detection"""
    
    print("🎯 TESTING CONTEXTUAL AMBIGUITY IMPROVEMENTS")
    print("=" * 60)
    print("Target: Improve detection rate from 25% to 70%+")
    print("=" * 60)
    
    # Contextual ambiguity test cases that previously failed
    contextual_cases = [
        {
            'text': 'The defendant\'s criminal history includes three prior convictions for theft.',
            'expected': 'Should recognize legitimate factual statement',
            'expected_detection': False,
            'category': 'legitimate_factual'
        },
        {
            'text': 'Given his criminal history, we can expect this type of behavior to continue.',
            'expected': 'Should detect biased prediction based on history',
            'expected_detection': True,
            'category': 'predictive_bias'
        },
        {
            'text': 'Community standards require us to consider the impact on local families.',
            'expected': 'Should recognize legitimate legal consideration',
            'expected_detection': False,
            'category': 'legitimate_legal'
        },
        {
            'text': 'People from that community don\'t share our community standards.',
            'expected': 'Should detect othering and cultural bias',
            'expected_detection': True,
            'category': 'cultural_othering'
        },
        # Additional challenging cases
        {
            'text': 'Based on his background, this behavior is predictable and typical.',
            'expected': 'Should detect background-based prediction bias',
            'expected_detection': True,
            'category': 'background_bias'
        },
        {
            'text': 'The witness testimony is consistent with court procedures.',
            'expected': 'Should recognize legitimate procedural language',
            'expected_detection': False,
            'category': 'legitimate_procedural'
        },
        {
            'text': 'As expected from someone of his type, he shows no remorse.',
            'expected': 'Should detect type-based assumption bias',
            'expected_detection': True,
            'category': 'type_assumption'
        },
        {
            'text': 'Legal representation quality varies based on financial resources.',
            'expected': 'Should recognize legitimate observation',
            'expected_detection': False,
            'category': 'legitimate_observation'
        }
    ]
    
    detector = AdvancedBiasDetector()
    
    correct_detections = 0
    total_cases = len(contextual_cases)
    
    for i, case in enumerate(contextual_cases, 1):
        print(f"\nTest {i}: {case['category']}")
        print(f"Text: '{case['text']}'")
        print(f"Expected: {case['expected']}")
        print(f"Should detect bias: {case['expected_detection']}")
        
        # Test with advanced detector
        detections = detector.advanced_bias_detection(case['text'], 'Test_Speaker')
        
        bias_detected = len(detections) > 0
        is_correct = bias_detected == case['expected_detection']
        
        if is_correct:
            correct_detections += 1
            print("✅ CORRECT PREDICTION")
        else:
            print("❌ INCORRECT PREDICTION")
        
        if detections:
            print("🔍 DETECTION DETAILS:")
            for detection in detections[:2]:  # Show top 2
                print(f"  • Type: {detection.bias_type}")
                print(f"  • Confidence: {detection.confidence_score:.2f}")
                print(f"  • Legal Context Score: {detection.legal_context_score:.2f}")
                print(f"  • Neutrality Score: {detection.neutrality_score:.2f}")
                print(f"  • Recommendation: {detection.recommendation}")
        else:
            print("🔍 NO BIAS DETECTED")
    
    accuracy = (correct_detections / total_cases) * 100
    print(f"\n📊 CONTEXTUAL AMBIGUITY PERFORMANCE:")
    print(f"   Correct Predictions: {correct_detections}/{total_cases}")
    print(f"   Accuracy: {accuracy:.1f}%")
    print(f"   Target: 70%+ (Previous: 25%)")
    
    if accuracy >= 70:
        print("🎉 TARGET ACHIEVED!")
    else:
        print("⚠️ Needs further improvement")
    
    return accuracy

def test_false_positive_reduction():
    """Test false positive reduction capabilities"""
    
    print(f"\n\n🛡️ TESTING FALSE POSITIVE REDUCTION")
    print("=" * 60)
    print("Target: Reduce false positives while maintaining detection rate")
    print("=" * 60)
    
    # Cases that should NOT be detected as bias (neutral/legitimate)
    neutral_cases = [
        {
            'text': 'Street vendors typically have cash-based transactions, making records unclear.',
            'category': 'legitimate_occupational_observation'
        },
        {
            'text': 'Elderly witnesses sometimes have memory issues that affect testimony.',
            'category': 'legitimate_age_consideration'
        },
        {
            'text': 'The witness works multiple jobs to support her family.',
            'category': 'neutral_economic_description'
        },
        {
            'text': 'His religious practices might influence his testimony timing.',
            'category': 'legitimate_scheduling_consideration'
        },
        {
            'text': 'Language barriers may require interpreter services.',
            'category': 'legitimate_communication_consideration'
        },
        {
            'text': 'Documentation varies across different business types.',
            'category': 'legitimate_business_observation'
        },
        {
            'text': 'Court procedures require advance notice for scheduling.',
            'category': 'legitimate_procedural_requirement'
        },
        {
            'text': 'Evidence collection follows standard forensic protocols.',
            'category': 'legitimate_procedural_language'
        }
    ]
    
    detector = AdvancedBiasDetector()
    
    false_positives = 0
    total_neutral = len(neutral_cases)
    
    for i, case in enumerate(neutral_cases, 1):
        print(f"\nTest {i}: {case['category']}")
        print(f"Text: '{case['text']}'")
        print("Expected: NO BIAS DETECTION")
        
        detections = detector.advanced_bias_detection(case['text'], 'Test_Speaker')
        
        if detections:
            false_positives += 1
            print("❌ FALSE POSITIVE DETECTED")
            for detection in detections:
                print(f"  • Type: {detection.bias_type}")
                print(f"  • Confidence: {detection.confidence_score:.2f}")
                print(f"  • Neutrality Score: {detection.neutrality_score:.2f}")
        else:
            print("✅ CORRECTLY IDENTIFIED AS NEUTRAL")
    
    false_positive_rate = (false_positives / total_neutral) * 100
    print(f"\n📊 FALSE POSITIVE ANALYSIS:")
    print(f"   False Positives: {false_positives}/{total_neutral}")
    print(f"   False Positive Rate: {false_positive_rate:.1f}%")
    print(f"   Target: <10% (Previous: ~7.5%)")
    
    if false_positive_rate < 10:
        print("🎉 TARGET ACHIEVED!")
    else:
        print("⚠️ Needs improvement in neutral case filtering")
    
    return false_positive_rate

def test_overall_performance_improvement():
    """Test overall performance improvement across all categories"""
    
    print(f"\n\n📈 TESTING OVERALL PERFORMANCE IMPROVEMENT")
    print("=" * 60)
    
    # Mixed test cases covering various bias types
    mixed_cases = [
        # Subtle implicit bias
        {'text': 'The witness seems quite articulate for someone from his background.', 'should_detect': True},
        {'text': 'She\'s surprisingly well-educated given her family circumstances.', 'should_detect': True},
        
        # Intersectional bias
        {'text': 'Young Muslim men from poor families often get involved in such activities.', 'should_detect': True},
        {'text': 'Elderly Dalit women are usually more submissive in court proceedings.', 'should_detect': True},
        
        # Coded language
        {'text': 'The defendant has that inner-city vibe, if you know what I mean.', 'should_detect': True},
        {'text': 'She has a very cosmopolitan outlook, unlike others from her area.', 'should_detect': True},
        
        # Regional/cultural
        {'text': 'These Punjabis are always involved in such disputes.', 'should_detect': True},
        {'text': 'People from the Northeast have different cultural values.', 'should_detect': True},
        
        # Professional bias
        {'text': 'As a taxi driver, he probably doesn\'t understand complex legal procedures.', 'should_detect': True},
        {'text': 'Being a domestic worker, she might not be reliable with time commitments.', 'should_detect': True},
        
        # Neutral cases
        {'text': 'The defendant\'s criminal history includes three prior convictions.', 'should_detect': False},
        {'text': 'Court procedures require advance notice for scheduling.', 'should_detect': False},
        {'text': 'The witness works multiple jobs to support her family.', 'should_detect': False},
        {'text': 'Evidence collection follows standard forensic protocols.', 'should_detect': False},
        
        # Contextual ambiguity
        {'text': 'Given his criminal history, we can expect this type of behavior to continue.', 'should_detect': True},
        {'text': 'People from that community don\'t share our community standards.', 'should_detect': True}
    ]
    
    detector = AdvancedBiasDetector()
    
    correct_predictions = 0
    total_cases = len(mixed_cases)
    true_positives = 0
    false_positives = 0
    true_negatives = 0
    false_negatives = 0
    
    for i, case in enumerate(mixed_cases, 1):
        detections = detector.advanced_bias_detection(case['text'], 'Test_Speaker')
        bias_detected = len(detections) > 0
        should_detect = case['should_detect']
        
        if bias_detected and should_detect:
            true_positives += 1
            correct_predictions += 1
        elif not bias_detected and not should_detect:
            true_negatives += 1
            correct_predictions += 1
        elif bias_detected and not should_detect:
            false_positives += 1
        else:  # not bias_detected and should_detect
            false_negatives += 1
    
    # Calculate metrics
    accuracy = (correct_predictions / total_cases) * 100
    precision = (true_positives / (true_positives + false_positives)) * 100 if (true_positives + false_positives) > 0 else 0
    recall = (true_positives / (true_positives + false_negatives)) * 100 if (true_positives + false_negatives) > 0 else 0
    f1_score = (2 * precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    print(f"📊 OVERALL PERFORMANCE METRICS:")
    print(f"   Total Cases: {total_cases}")
    print(f"   Accuracy: {accuracy:.1f}%")
    print(f"   Precision: {precision:.1f}%")
    print(f"   Recall: {recall:.1f}%")
    print(f"   F1-Score: {f1_score:.1f}")
    print(f"\n📈 CONFUSION MATRIX:")
    print(f"   True Positives: {true_positives}")
    print(f"   True Negatives: {true_negatives}")
    print(f"   False Positives: {false_positives}")
    print(f"   False Negatives: {false_negatives}")
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'true_positives': true_positives,
        'true_negatives': true_negatives,
        'false_positives': false_positives,
        'false_negatives': false_negatives
    }

def test_advanced_features():
    """Test advanced features like legal context scoring and recommendations"""
    
    print(f"\n\n🔬 TESTING ADVANCED FEATURES")
    print("=" * 60)
    
    test_cases = [
        {
            'text': 'Given his criminal history, we can expect this type of behavior to continue.',
            'expected_features': 'High bias confidence, low legal context score, clear recommendation'
        },
        {
            'text': 'The defendant\'s criminal history includes three prior convictions for theft.',
            'expected_features': 'High legal context score, low bias confidence, neutral recommendation'
        },
        {
            'text': 'The witness seems quite articulate for someone from his background.',
            'expected_features': 'Medium bias confidence, semantic analysis detection, uncertainty quantification'
        }
    ]
    
    detector = AdvancedBiasDetector()
    
    for i, case in enumerate(test_cases, 1):
        print(f"\nAdvanced Feature Test {i}:")
        print(f"Text: '{case['text']}'")
        print(f"Expected: {case['expected_features']}")
        
        detections = detector.advanced_bias_detection(case['text'], 'Test_Speaker')
        
        if detections:
            detection = detections[0]  # Show first detection
            print("🔬 ADVANCED FEATURES:")
            print(f"  • Confidence Score: {detection.confidence_score:.2f}")
            print(f"  • Legal Context Score: {detection.legal_context_score:.2f}")
            print(f"  • Semantic Confidence: {detection.semantic_confidence:.2f}")
            print(f"  • Neutrality Score: {detection.neutrality_score:.2f}")
            print(f"  • Uncertainty Score: {detection.uncertainty_score:.2f}")
            print(f"  • Recommendation: {detection.recommendation}")
            print(f"  • Detection Method: {detection.detection_method}")
        else:
            print("🔬 NO DETECTION - ADVANCED ANALYSIS:")
            # Still show legal context analysis
            legal_score = detector._analyze_legal_context(case['text'], case['text'].lower())
            print(f"  • Legal Context Score: {legal_score:.2f}")

def save_advanced_test_results(contextual_accuracy, false_positive_rate, overall_metrics):
    """Save advanced system test results"""
    
    results = {
        'advanced_system_performance': {
            'contextual_ambiguity_accuracy': contextual_accuracy,
            'false_positive_rate': false_positive_rate,
            'overall_metrics': overall_metrics,
            'improvement_summary': {
                'contextual_ambiguity': f"Improved from 25% to {contextual_accuracy:.1f}%",
                'false_positive_control': f"Maintained at {false_positive_rate:.1f}%",
                'overall_accuracy': f"Achieved {overall_metrics['accuracy']:.1f}%",
                'precision': f"Achieved {overall_metrics['precision']:.1f}%",
                'recall': f"Achieved {overall_metrics['recall']:.1f}%"
            }
        },
        'next_iteration_priorities': [
            'Real-time performance optimization',
            'Adaptive learning implementation',
            'Advanced semantic analysis with NLP models',
            'Comprehensive testing framework',
            'Edge case handling improvements'
        ]
    }
    
    try:
        with open('advanced_system_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Advanced system results saved to: advanced_system_results.json")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")

def main():
    """Main testing function for advanced system"""
    
    print("🚀 ADVANCED BIAS DETECTION SYSTEM - NEXT ITERATION TESTING")
    print("=" * 80)
    print("Testing improvements in contextual understanding and false positive reduction...")
    print("=" * 80)
    
    # Test contextual ambiguity improvements
    contextual_accuracy = test_contextual_ambiguity_improvements()
    
    # Test false positive reduction
    false_positive_rate = test_false_positive_reduction()
    
    # Test overall performance
    overall_metrics = test_overall_performance_improvement()
    
    # Test advanced features
    test_advanced_features()
    
    # Save results
    save_advanced_test_results(contextual_accuracy, false_positive_rate, overall_metrics)
    
    # Final summary
    print(f"\n\n🎉 ADVANCED SYSTEM TESTING COMPLETE!")
    print("=" * 80)
    print(f"📊 KEY IMPROVEMENTS:")
    print(f"   • Contextual Ambiguity: {contextual_accuracy:.1f}% (Target: 70%+)")
    print(f"   • False Positive Rate: {false_positive_rate:.1f}% (Target: <10%)")
    print(f"   • Overall Accuracy: {overall_metrics['accuracy']:.1f}%")
    print(f"   • Precision: {overall_metrics['precision']:.1f}%")
    print(f"   • Recall: {overall_metrics['recall']:.1f}%")
    
    print(f"\n🎯 NEXT ITERATION STATUS:")
    if contextual_accuracy >= 70 and false_positive_rate < 10 and overall_metrics['accuracy'] > 85:
        print("   ✅ READY FOR NEXT PHASE: Real-time optimization and adaptive learning")
    else:
        print("   ⚠️ NEEDS REFINEMENT: Continue improving current features")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
