"""
Analysis of how different summarization approaches affect bias detection
Tests various summarization strategies and their impact on bias preservation
"""

from courtroom_bias_detector import integrate_bias_detection
import json

def create_original_diarized_text():
    """Original full diarized text"""
    return [
        {
            'speaker': 'Judge',
            'text': 'The court is now in session. We are hearing the case of theft against Mr. <PERSON><PERSON>.',
            'timestamp': '00:01:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Your honor, the defendant comes from a scheduled caste background. These people typically engage in such criminal activities due to their inherent nature.',
            'timestamp': '00:02:30'
        },
        {
            'speaker': 'Judge',
            'text': 'The objection is sustained. However, I must note that the female witness seemed quite emotional. Women are generally more emotional and less reliable in such serious matters.',
            'timestamp': '00:04:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Your honor, this Muslim defendant clearly shows the typical deceptive behavior of his community. They are known for such tactics.',
            'timestamp': '00:06:45'
        },
        {
            'speaker': 'Judge',
            'text': 'People from poor economic backgrounds often resort to crime due to their circumstances and mentality.',
            'timestamp': '00:08:15'
        }
    ]

def create_summarization_variants():
    """Create different summarization approaches"""
    
    # Variant 1: Factual Summary (removes opinions/bias)
    factual_summary = [
        {
            'speaker': 'Judge',
            'text': 'Court session opened for theft case against Mr. Rajesh Kumar.',
            'timestamp': '00:01:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Presented arguments about defendant background and behavior patterns.',
            'timestamp': '00:02:30-00:06:45'
        },
        {
            'speaker': 'Judge',
            'text': 'Made observations about witness reliability and socioeconomic factors.',
            'timestamp': '00:04:00-00:08:15'
        }
    ]
    
    # Variant 2: Detailed Summary (preserves some context)
    detailed_summary = [
        {
            'speaker': 'Judge',
            'text': 'Court session opened for theft case. Noted concerns about emotional female witness reliability.',
            'timestamp': '00:01:00-00:04:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Argued defendant from scheduled caste shows typical criminal behavior. Claimed Muslim community exhibits deceptive tactics.',
            'timestamp': '00:02:30-00:06:45'
        },
        {
            'speaker': 'Judge',
            'text': 'Observed that poor economic background leads to criminal mentality.',
            'timestamp': '00:08:15'
        }
    ]
    
    # Variant 3: Key Quotes Summary (preserves exact biased statements)
    key_quotes_summary = [
        {
            'speaker': 'Prosecutor',
            'text': 'These people typically engage in such criminal activities due to their inherent nature.',
            'timestamp': '00:02:30'
        },
        {
            'speaker': 'Judge',
            'text': 'Women are generally more emotional and less reliable in such serious matters.',
            'timestamp': '00:04:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'This Muslim defendant clearly shows the typical deceptive behavior of his community.',
            'timestamp': '00:06:45'
        },
        {
            'speaker': 'Judge',
            'text': 'People from poor economic backgrounds often resort to crime due to their mentality.',
            'timestamp': '00:08:15'
        }
    ]
    
    return {
        'factual': factual_summary,
        'detailed': detailed_summary,
        'key_quotes': key_quotes_summary
    }

def analyze_summarization_variant(variant_name, variant_data, original_results):
    """Analyze a specific summarization variant"""
    
    print(f"\n📊 ANALYZING: {variant_name.upper()} SUMMARIZATION")
    print("-" * 50)
    
    # Run bias detection on variant
    variant_results = integrate_bias_detection(variant_data, enable_ml=False)
    
    # Extract metrics
    original_detections = original_results['bias_results']['detections']
    variant_detections = variant_results['bias_results']['detections']
    
    original_bias_types = set(d.bias_type for d in original_detections)
    variant_bias_types = set(d.bias_type for d in variant_detections)
    
    # Calculate preservation metrics
    preserved_types = len(variant_bias_types & original_bias_types)
    lost_types = len(original_bias_types - variant_bias_types)
    preservation_rate = (preserved_types / len(original_bias_types) * 100) if original_bias_types else 0
    
    # Calculate text reduction
    original_chars = sum(len(item['text']) for item in create_original_diarized_text())
    variant_chars = sum(len(item['text']) for item in variant_data)
    text_reduction = (1 - variant_chars / original_chars) * 100
    
    print(f"📈 METRICS:")
    print(f"   Text reduction: {text_reduction:.1f}%")
    print(f"   Original bias instances: {len(original_detections)}")
    print(f"   Preserved bias instances: {len(variant_detections)}")
    print(f"   Bias preservation rate: {preservation_rate:.1f}%")
    print(f"   Bias types preserved: {preserved_types}/{len(original_bias_types)}")
    
    if lost_types > 0:
        lost_bias_types = original_bias_types - variant_bias_types
        print(f"   Lost bias types: {', '.join(lost_bias_types)}")
    
    # Show preserved bias instances
    if variant_detections:
        print(f"\n🔍 PRESERVED BIAS INSTANCES:")
        for i, detection in enumerate(variant_detections, 1):
            print(f"   {i}. {detection.bias_type} ({detection.severity}, {detection.confidence_score:.2f})")
            print(f"      '{detection.instances[0][:80]}...'")
    else:
        print(f"\n❌ NO BIAS PRESERVED")
    
    return {
        'variant_name': variant_name,
        'text_reduction': text_reduction,
        'preservation_rate': preservation_rate,
        'preserved_instances': len(variant_detections),
        'lost_types': lost_types,
        'preserved_bias_types': list(variant_bias_types),
        'lost_bias_types': list(original_bias_types - variant_bias_types)
    }

def generate_comprehensive_recommendation(analysis_results):
    """Generate comprehensive recommendation based on all analyses"""
    
    print(f"\n🎯 COMPREHENSIVE RECOMMENDATION")
    print("=" * 60)
    
    print(f"\n📊 SUMMARIZATION VARIANT COMPARISON:")
    print("-" * 40)
    
    for result in analysis_results:
        print(f"\n{result['variant_name'].upper()}:")
        print(f"   Text reduction: {result['text_reduction']:.1f}%")
        print(f"   Bias preservation: {result['preservation_rate']:.1f}%")
        print(f"   Preserved instances: {result['preserved_instances']}")
        print(f"   Lost bias types: {len(result['lost_bias_types'])}")
    
    # Find best performing variant
    best_variant = max(analysis_results, key=lambda x: x['preservation_rate'])
    
    print(f"\n🏆 BEST PERFORMING SUMMARIZATION: {best_variant['variant_name'].upper()}")
    print(f"   Preserves {best_variant['preservation_rate']:.1f}% of bias information")
    print(f"   Reduces text by {best_variant['text_reduction']:.1f}%")
    
    print(f"\n🔍 DETAILED ANALYSIS:")
    
    # Determine recommendation based on preservation rates
    if best_variant['preservation_rate'] >= 80:
        recommendation = f"SUMMARIZED TEXT ({best_variant['variant_name']} approach)"
        confidence = "MEDIUM"
        reason = "Good bias preservation with significant efficiency gains"
    elif best_variant['preservation_rate'] >= 60:
        recommendation = "HYBRID APPROACH"
        confidence = "MEDIUM"
        reason = "Use summarized text for efficiency, diarized text for critical analysis"
    else:
        recommendation = "DIARIZED TEXT"
        confidence = "HIGH"
        reason = "Summarization loses too much bias information"
    
    print(f"   • Best summarization preserves: {best_variant['preservation_rate']:.1f}% of bias")
    print(f"   • Text efficiency gain: {best_variant['text_reduction']:.1f}% reduction")
    print(f"   • Information loss: {'ACCEPTABLE' if best_variant['preservation_rate'] >= 70 else 'SIGNIFICANT'}")
    
    print(f"\n🎯 FINAL RECOMMENDATION: {recommendation}")
    print(f"   Confidence: {confidence}")
    print(f"   Reason: {reason}")
    
    # Provide implementation guidance
    print(f"\n💡 IMPLEMENTATION GUIDANCE:")
    
    if "DIARIZED" in recommendation:
        print("   ✅ Use full diarized text for bias detection")
        print("   ✅ Apply bias detection before summarization")
        print("   ✅ Include bias analysis in final report alongside summary")
        print("   ⚠️  Higher processing time but maximum accuracy")
    
    elif "HYBRID" in recommendation:
        print("   ✅ Use diarized text for bias detection")
        print("   ✅ Use summarized text for content analysis")
        print("   ✅ Run bias detection on both for comparison")
        print("   ⚠️  Balanced approach - good accuracy with efficiency")
    
    else:  # SUMMARIZED
        print(f"   ✅ Use {best_variant['variant_name']} summarization approach")
        print("   ✅ Ensure summarization preserves biased statements")
        print("   ✅ Apply bias detection to summarized text")
        print("   ⚠️  Faster processing but some bias information loss")
    
    return recommendation, best_variant

def main():
    """Main analysis function"""
    
    print("🔬 SUMMARIZATION IMPACT ON BIAS DETECTION")
    print("=" * 60)
    print("Analyzing how different summarization approaches affect")
    print("bias detection accuracy and coverage...")
    print("=" * 60)
    
    # Get original diarized text and run bias detection
    original_text = create_original_diarized_text()
    original_results = integrate_bias_detection(original_text, enable_ml=False)
    
    print(f"\n📄 BASELINE (ORIGINAL DIARIZED TEXT):")
    print(f"   Total segments: {len(original_text)}")
    print(f"   Total characters: {sum(len(item['text']) for item in original_text)}")
    print(f"   Bias instances detected: {len(original_results['bias_results']['detections'])}")
    
    original_bias_types = set(d.bias_type for d in original_results['bias_results']['detections'])
    print(f"   Unique bias types: {len(original_bias_types)}")
    print(f"   Bias types: {', '.join(original_bias_types)}")
    
    # Test different summarization variants
    variants = create_summarization_variants()
    analysis_results = []
    
    for variant_name, variant_data in variants.items():
        result = analyze_summarization_variant(variant_name, variant_data, original_results)
        analysis_results.append(result)
    
    # Generate comprehensive recommendation
    recommendation, best_variant = generate_comprehensive_recommendation(analysis_results)
    
    # Save results
    final_results = {
        'baseline': {
            'segments': len(original_text),
            'characters': sum(len(item['text']) for item in original_text),
            'bias_instances': len(original_results['bias_results']['detections']),
            'bias_types': list(original_bias_types)
        },
        'variant_analysis': analysis_results,
        'recommendation': recommendation,
        'best_variant': best_variant
    }
    
    try:
        with open('summarization_impact_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Analysis results saved to: summarization_impact_analysis.json")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")
    
    print(f"\n🎉 ANALYSIS COMPLETE!")
    print("=" * 60)

if __name__ == "__main__":
    main()
