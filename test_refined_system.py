"""
Test Refined Bias Detection System - Targeted Fixes
Focus: Specific fixes for contextual ambiguity and false positive issues
"""

from refined_bias_detector import refined_bias_detection, RefinedBiasDetector
import json

def test_specific_problem_cases():
    """Test the specific cases that were failing"""
    
    print("🎯 TESTING SPECIFIC PROBLEM CASE FIXES")
    print("=" * 60)
    
    # Specific problem cases from previous testing
    problem_cases = [
        # CONTEXTUAL AMBIGUITY - Previously missed
        {
            'text': 'Given his criminal history, we can expect this type of behavior to continue.',
            'expected_detection': True,
            'issue': 'Missed predictive bias',
            'category': 'predictive_bias'
        },
        {
            'text': 'People from that community don\'t share our community standards.',
            'expected_detection': True,
            'issue': 'Missed othering bias',
            'category': 'othering_bias'
        },
        {
            'text': 'As expected from someone of his type, he shows no remorse.',
            'expected_detection': True,
            'issue': 'Missed type assumption bias',
            'category': 'type_assumption'
        },
        {
            'text': 'Based on his background, this behavior is predictable and typical.',
            'expected_detection': True,
            'issue': 'Missed background-based prediction',
            'category': 'background_prediction'
        },
        
        # FALSE POSITIVES - Previously incorrectly flagged
        {
            'text': 'Street vendors typically have cash-based transactions, making records unclear.',
            'expected_detection': False,
            'issue': 'False positive - legitimate business observation',
            'category': 'business_observation'
        },
        {
            'text': 'Elderly witnesses sometimes have memory issues that affect testimony.',
            'expected_detection': False,
            'issue': 'False positive - legitimate age consideration',
            'category': 'age_consideration'
        },
        
        # CONTROL CASES - Should remain unchanged
        {
            'text': 'The defendant\'s criminal history includes three prior convictions for theft.',
            'expected_detection': False,
            'issue': 'Control - legitimate factual statement',
            'category': 'factual_statement'
        },
        {
            'text': 'The witness seems quite articulate for someone from his background.',
            'expected_detection': True,
            'issue': 'Control - should still detect implicit bias',
            'category': 'implicit_bias'
        }
    ]
    
    detector = RefinedBiasDetector()
    
    fixed_issues = 0
    total_problems = len(problem_cases)
    
    for i, case in enumerate(problem_cases, 1):
        print(f"\nProblem Case {i}: {case['category']}")
        print(f"Text: '{case['text']}'")
        print(f"Issue: {case['issue']}")
        print(f"Expected Detection: {case['expected_detection']}")
        
        # Test with refined detector
        detections = detector.refined_bias_detection(case['text'], 'Test_Speaker')
        
        bias_detected = len(detections) > 0
        is_fixed = bias_detected == case['expected_detection']
        
        if is_fixed:
            fixed_issues += 1
            print("✅ ISSUE FIXED")
        else:
            print("❌ ISSUE PERSISTS")
        
        if detections:
            print("🔍 DETECTION DETAILS:")
            for detection in detections[:1]:  # Show first detection
                print(f"  • Type: {detection.bias_type}")
                print(f"  • Confidence: {detection.confidence_score:.2f}")
                print(f"  • Legal Context: {detection.legal_context_score:.2f}")
                print(f"  • Neutrality: {detection.neutrality_score:.2f}")
                print(f"  • Recommendation: {detection.recommendation}")
        else:
            print("🔍 NO BIAS DETECTED")
    
    fix_rate = (fixed_issues / total_problems) * 100
    print(f"\n📊 PROBLEM CASE FIX RATE:")
    print(f"   Fixed Issues: {fixed_issues}/{total_problems}")
    print(f"   Fix Rate: {fix_rate:.1f}%")
    
    return fix_rate

def test_refined_contextual_ambiguity():
    """Test refined contextual ambiguity performance"""
    
    print(f"\n\n🎯 TESTING REFINED CONTEXTUAL AMBIGUITY")
    print("=" * 60)
    
    # Extended contextual ambiguity test cases
    contextual_cases = [
        # Should detect bias
        {'text': 'Given his criminal history, we can expect this type of behavior to continue.', 'should_detect': True},
        {'text': 'People from that community don\'t share our community standards.', 'should_detect': True},
        {'text': 'As expected from someone of his type, he shows no remorse.', 'should_detect': True},
        {'text': 'Based on his background, this behavior is predictable and typical.', 'should_detect': True},
        {'text': 'His kind usually behave this way in court proceedings.', 'should_detect': True},
        {'text': 'We can predict his response based on his community background.', 'should_detect': True},
        
        # Should NOT detect bias (legitimate)
        {'text': 'The defendant\'s criminal history includes three prior convictions for theft.', 'should_detect': False},
        {'text': 'Community standards require us to consider the impact on local families.', 'should_detect': False},
        {'text': 'The witness testimony is consistent with court procedures.', 'should_detect': False},
        {'text': 'Legal representation quality varies based on financial resources.', 'should_detect': False},
        {'text': 'Court procedures require advance notice for scheduling.', 'should_detect': False},
        {'text': 'Evidence collection follows standard forensic protocols.', 'should_detect': False}
    ]
    
    detector = RefinedBiasDetector()
    
    correct_predictions = 0
    total_cases = len(contextual_cases)
    
    for i, case in enumerate(contextual_cases, 1):
        detections = detector.refined_bias_detection(case['text'], 'Test_Speaker')
        bias_detected = len(detections) > 0
        is_correct = bias_detected == case['should_detect']
        
        if is_correct:
            correct_predictions += 1
    
    accuracy = (correct_predictions / total_cases) * 100
    print(f"📊 REFINED CONTEXTUAL AMBIGUITY PERFORMANCE:")
    print(f"   Correct Predictions: {correct_predictions}/{total_cases}")
    print(f"   Accuracy: {accuracy:.1f}%")
    print(f"   Target: 70%+ (Previous: 50%)")
    
    if accuracy >= 70:
        print("🎉 TARGET ACHIEVED!")
    else:
        print("⚠️ Still needs improvement")
    
    return accuracy

def test_refined_false_positive_rate():
    """Test refined false positive rate"""
    
    print(f"\n\n🛡️ TESTING REFINED FALSE POSITIVE RATE")
    print("=" * 60)
    
    # Neutral cases that should NOT be detected as bias
    neutral_cases = [
        'Street vendors typically have cash-based transactions, making records unclear.',
        'Elderly witnesses sometimes have memory issues that affect testimony.',
        'The witness works multiple jobs to support her family.',
        'His religious practices might influence his testimony timing.',
        'Language barriers may require interpreter services.',
        'Documentation varies across different business types.',
        'Court procedures require advance notice for scheduling.',
        'Evidence collection follows standard forensic protocols.',
        'Seasonal work affects income documentation availability.',
        'Memory considerations are important for elderly testimony.',
        'Cash-based businesses have different record-keeping practices.',
        'Physical limitations may affect witness comfort during testimony.'
    ]
    
    detector = RefinedBiasDetector()
    
    false_positives = 0
    total_neutral = len(neutral_cases)
    
    for i, text in enumerate(neutral_cases, 1):
        detections = detector.refined_bias_detection(text, 'Test_Speaker')
        
        if detections:
            false_positives += 1
    
    false_positive_rate = (false_positives / total_neutral) * 100
    print(f"📊 REFINED FALSE POSITIVE ANALYSIS:")
    print(f"   False Positives: {false_positives}/{total_neutral}")
    print(f"   False Positive Rate: {false_positive_rate:.1f}%")
    print(f"   Target: <10% (Previous: 25%)")
    
    if false_positive_rate < 10:
        print("🎉 TARGET ACHIEVED!")
    else:
        print("⚠️ Still needs improvement")
    
    return false_positive_rate

def test_refined_overall_performance():
    """Test overall performance of refined system"""
    
    print(f"\n\n📈 TESTING REFINED OVERALL PERFORMANCE")
    print("=" * 60)
    
    # Comprehensive test set
    test_cases = [
        # Bias cases (should detect)
        {'text': 'The witness seems quite articulate for someone from his background.', 'should_detect': True},
        {'text': 'Given his criminal history, we can expect this type of behavior to continue.', 'should_detect': True},
        {'text': 'People from that community don\'t share our community standards.', 'should_detect': True},
        {'text': 'As expected from someone of his type, he shows no remorse.', 'should_detect': True},
        {'text': 'Young Muslim men from poor families often get involved in such activities.', 'should_detect': True},
        {'text': 'These Punjabis are always involved in such disputes.', 'should_detect': True},
        {'text': 'The defendant has that inner-city vibe, if you know what I mean.', 'should_detect': True},
        {'text': 'As a taxi driver, he probably doesn\'t understand complex legal procedures.', 'should_detect': True},
        {'text': 'The female witness seems quite emotional about the incident.', 'should_detect': True},
        {'text': 'Young people today don\'t respect authority like previous generations.', 'should_detect': True},
        
        # Neutral cases (should NOT detect)
        {'text': 'The defendant\'s criminal history includes three prior convictions.', 'should_detect': False},
        {'text': 'Street vendors typically have cash-based transactions, making records unclear.', 'should_detect': False},
        {'text': 'Elderly witnesses sometimes have memory issues that affect testimony.', 'should_detect': False},
        {'text': 'Court procedures require advance notice for scheduling.', 'should_detect': False},
        {'text': 'The witness works multiple jobs to support her family.', 'should_detect': False},
        {'text': 'Evidence collection follows standard forensic protocols.', 'should_detect': False},
        {'text': 'Language barriers may require interpreter services.', 'should_detect': False},
        {'text': 'Documentation varies across different business types.', 'should_detect': False}
    ]
    
    detector = RefinedBiasDetector()
    
    true_positives = 0
    true_negatives = 0
    false_positives = 0
    false_negatives = 0
    
    for case in test_cases:
        detections = detector.refined_bias_detection(case['text'], 'Test_Speaker')
        bias_detected = len(detections) > 0
        should_detect = case['should_detect']
        
        if bias_detected and should_detect:
            true_positives += 1
        elif not bias_detected and not should_detect:
            true_negatives += 1
        elif bias_detected and not should_detect:
            false_positives += 1
        else:  # not bias_detected and should_detect
            false_negatives += 1
    
    # Calculate metrics
    total_cases = len(test_cases)
    accuracy = ((true_positives + true_negatives) / total_cases) * 100
    precision = (true_positives / (true_positives + false_positives)) * 100 if (true_positives + false_positives) > 0 else 0
    recall = (true_positives / (true_positives + false_negatives)) * 100 if (true_positives + false_negatives) > 0 else 0
    f1_score = (2 * precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    print(f"📊 REFINED OVERALL PERFORMANCE:")
    print(f"   Total Cases: {total_cases}")
    print(f"   Accuracy: {accuracy:.1f}%")
    print(f"   Precision: {precision:.1f}%")
    print(f"   Recall: {recall:.1f}%")
    print(f"   F1-Score: {f1_score:.1f}")
    print(f"\n📈 CONFUSION MATRIX:")
    print(f"   True Positives: {true_positives}")
    print(f"   True Negatives: {true_negatives}")
    print(f"   False Positives: {false_positives}")
    print(f"   False Negatives: {false_negatives}")
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'confusion_matrix': {
            'true_positives': true_positives,
            'true_negatives': true_negatives,
            'false_positives': false_positives,
            'false_negatives': false_negatives
        }
    }

def save_refined_results(fix_rate, contextual_accuracy, false_positive_rate, overall_metrics):
    """Save refined system test results"""
    
    results = {
        'refined_system_performance': {
            'problem_case_fix_rate': fix_rate,
            'contextual_ambiguity_accuracy': contextual_accuracy,
            'false_positive_rate': false_positive_rate,
            'overall_metrics': overall_metrics,
            'targets_achieved': {
                'contextual_ambiguity': contextual_accuracy >= 70,
                'false_positive_rate': false_positive_rate < 10,
                'overall_accuracy': overall_metrics['accuracy'] > 85
            }
        },
        'system_evolution': {
            'original_enhanced': '7.5% detection rate',
            'improved_system': '82.5% detection rate, 25% false positive rate',
            'advanced_system': '87.5% accuracy, 25% false positive rate, 50% contextual accuracy',
            'refined_system': f"{overall_metrics['accuracy']:.1f}% accuracy, {false_positive_rate:.1f}% false positive rate, {contextual_accuracy:.1f}% contextual accuracy"
        },
        'next_steps': [
            'Deploy refined system for real-world testing',
            'Implement real-time performance optimization',
            'Add adaptive learning capabilities',
            'Create comprehensive monitoring dashboard',
            'Develop user training materials'
        ]
    }
    
    try:
        with open('refined_system_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Refined system results saved to: refined_system_results.json")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")

def main():
    """Main testing function for refined system"""
    
    print("🔧 REFINED BIAS DETECTION SYSTEM - TARGETED FIXES")
    print("=" * 80)
    print("Testing specific fixes for contextual ambiguity and false positive issues...")
    print("=" * 80)
    
    # Test specific problem case fixes
    fix_rate = test_specific_problem_cases()
    
    # Test refined contextual ambiguity
    contextual_accuracy = test_refined_contextual_ambiguity()
    
    # Test refined false positive rate
    false_positive_rate = test_refined_false_positive_rate()
    
    # Test overall performance
    overall_metrics = test_refined_overall_performance()
    
    # Save results
    save_refined_results(fix_rate, contextual_accuracy, false_positive_rate, overall_metrics)
    
    # Final assessment
    print(f"\n\n🎉 REFINED SYSTEM TESTING COMPLETE!")
    print("=" * 80)
    print(f"📊 FINAL RESULTS:")
    print(f"   • Problem Case Fix Rate: {fix_rate:.1f}%")
    print(f"   • Contextual Ambiguity: {contextual_accuracy:.1f}% (Target: 70%+)")
    print(f"   • False Positive Rate: {false_positive_rate:.1f}% (Target: <10%)")
    print(f"   • Overall Accuracy: {overall_metrics['accuracy']:.1f}%")
    print(f"   • Precision: {overall_metrics['precision']:.1f}%")
    print(f"   • Recall: {overall_metrics['recall']:.1f}%")
    
    # Determine readiness for next phase
    targets_met = 0
    if contextual_accuracy >= 70:
        targets_met += 1
        print(f"   ✅ Contextual Ambiguity Target Met")
    else:
        print(f"   ❌ Contextual Ambiguity Target Missed")
    
    if false_positive_rate < 10:
        targets_met += 1
        print(f"   ✅ False Positive Target Met")
    else:
        print(f"   ❌ False Positive Target Missed")
    
    if overall_metrics['accuracy'] > 85:
        targets_met += 1
        print(f"   ✅ Overall Accuracy Target Met")
    else:
        print(f"   ❌ Overall Accuracy Target Missed")
    
    print(f"\n🎯 NEXT ITERATION STATUS:")
    if targets_met >= 2:
        print("   ✅ READY FOR NEXT PHASE: Real-time optimization and deployment preparation")
    else:
        print("   ⚠️ NEEDS FURTHER REFINEMENT: Continue improving core detection capabilities")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
