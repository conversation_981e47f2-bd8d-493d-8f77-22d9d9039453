"""
Performance demonstration of the hybrid bias detection system
Shows detection accuracy, speed, and different bias types
"""

from courtroom_bias_detector import HybridIndianCourtroomBiasDetector, quick_bias_check_hybrid
import time

def demo_bias_types():
    """Demonstrate detection of different bias types"""
    print("🎯 BIAS TYPE DETECTION DEMONSTRATION")
    print("="*60)
    
    test_cases = [
        {
            'category': 'GENDER BIAS',
            'examples': [
                "Women are naturally too emotional for important legal decisions",
                "Men are obviously better suited for leadership roles in court",
                "Typical female behavior - getting hysterical under pressure"
            ]
        },
        {
            'category': 'CASTE BIAS', 
            'examples': [
                "People from lower castes are inherently dishonest",
                "All Dalits are born criminals and cannot be trusted",
                "Upper caste people are naturally more intelligent"
            ]
        },
        {
            'category': 'RELIGIOUS BIAS',
            'examples': [
                "Muslims are always potential terrorists",
                "These people's religion teaches them violence",
                "Typical Hindu fundamentalist behavior"
            ]
        },
        {
            'category': 'ECONOMIC BIAS',
            'examples': [
                "Poor people always lie to get sympathy",
                "Rich people can obviously buy justice",
                "Typical slum mentality - always dishonest"
            ]
        },
        {
            'category': 'REGIONAL BIAS',
            'examples': [
                "These Bihari migrants are typical troublemakers",
                "South Indians have that typical stubborn mentality",
                "All these outsiders should go back to their states"
            ]
        },
        {
            'category': 'LANGUAGE BIAS',
            'examples': [
                "Cannot speak proper English so testimony is unreliable",
                "Typical village mentality with broken English",
                "English-speaking people are obviously more educated"
            ]
        }
    ]
    
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
    
    for category_data in test_cases:
        print(f"\n📍 {category_data['category']}")
        print("-" * 40)
        
        for i, example in enumerate(category_data['examples'], 1):
            print(f"\nExample {i}: '{example}'")
            
            detections = detector.detect_bias_in_text(example, "Test_Speaker")
            
            if detections:
                for detection in detections:
                    print(f"  ✗ DETECTED: {detection.bias_type}")
                    print(f"    Severity: {detection.severity.upper()}")
                    print(f"    Confidence: {detection.confidence_score:.2f}")
                    print(f"    Method: {detection.detection_method}")
            else:
                print("  ✓ No bias detected")

def demo_severity_levels():
    """Demonstrate different severity levels"""
    print("\n\n🔥 SEVERITY LEVEL DEMONSTRATION")
    print("="*60)
    
    severity_examples = [
        ("CRITICAL", "All Muslims are always terrorists and never trustworthy"),
        ("HIGH", "Women are typically emotional and generally unreliable"),
        ("MEDIUM", "Poor people often resort to dishonest means"),
        ("LOW", "Some people from that community might be problematic")
    ]
    
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
    
    for expected_severity, example in severity_examples:
        print(f"\n📊 Expected: {expected_severity}")
        print(f"Statement: '{example}'")
        
        detections = detector.detect_bias_in_text(example, "Test_Speaker")
        
        if detections:
            for detection in detections:
                print(f"  ✗ Detected: {detection.severity.upper()}")
                print(f"    Confidence: {detection.confidence_score:.2f}")
                print(f"    Bias Type: {detection.bias_type}")
        else:
            print("  ✓ No bias detected")

def demo_performance_speed():
    """Demonstrate system performance and speed"""
    print("\n\n⚡ PERFORMANCE & SPEED DEMONSTRATION")
    print("="*60)
    
    # Test statements of varying lengths
    test_statements = [
        "Short bias test: Women are emotional.",
        "Medium length bias test: The defendant, being from a lower caste background, is naturally inclined towards criminal behavior and cannot be trusted.",
        "Long bias test: " + " ".join([
            "In this courtroom, we have seen many cases where people from certain communities",
            "exhibit typical behavioral patterns. Women witnesses are generally emotional and unreliable,",
            "while men from lower castes are naturally dishonest. Muslims often show deceptive behavior",
            "typical of their religious background. Poor people always lie to gain sympathy,",
            "and these Bihari migrants are obviously troublemakers who cannot speak proper English."
        ])
    ]
    
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
    
    print("\n📈 Processing Speed Test:")
    print("-" * 30)
    
    for i, statement in enumerate(test_statements, 1):
        start_time = time.time()
        detections = detector.detect_bias_in_text(statement, f"Speaker_{i}")
        end_time = time.time()
        
        processing_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        print(f"\nTest {i} ({len(statement)} chars):")
        print(f"  Processing time: {processing_time:.2f}ms")
        print(f"  Bias instances found: {len(detections)}")
        
        if detections:
            bias_types = [d.bias_type for d in detections]
            avg_confidence = sum(d.confidence_score for d in detections) / len(detections)
            print(f"  Bias types: {', '.join(set(bias_types))}")
            print(f"  Average confidence: {avg_confidence:.2f}")

def demo_real_courtroom_scenario():
    """Demonstrate with a realistic courtroom scenario"""
    print("\n\n⚖️ REALISTIC COURTROOM SCENARIO")
    print("="*60)
    
    courtroom_dialogue = [
        {
            'speaker': 'Judge',
            'text': 'The court is now in session for the case of theft against Mr. Sharma.',
            'expected': 'No bias'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Your honor, the defendant comes from a scheduled caste background, and these people typically engage in such criminal activities.',
            'expected': 'Caste bias'
        },
        {
            'speaker': 'Defense_Lawyer',
            'text': 'Objection! My client should be judged on evidence, not his background.',
            'expected': 'No bias'
        },
        {
            'speaker': 'Judge',
            'text': 'Sustained. However, the female witness seems too emotional to provide reliable testimony.',
            'expected': 'Gender bias'
        },
        {
            'speaker': 'Witness',
            'text': 'I clearly saw the defendant helping an elderly person during the incident.',
            'expected': 'No bias'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'This Muslim defendant clearly shows the typical deceptive behavior of his community.',
            'expected': 'Religious bias'
        }
    ]
    
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
    
    print("\n🎭 Courtroom Dialogue Analysis:")
    print("-" * 40)
    
    total_bias_detected = 0
    total_statements = len(courtroom_dialogue)
    
    for i, dialogue in enumerate(courtroom_dialogue, 1):
        print(f"\n{i}. {dialogue['speaker']}: '{dialogue['text']}'")
        print(f"   Expected: {dialogue['expected']}")
        
        detections = detector.detect_bias_in_text(dialogue['text'], dialogue['speaker'])
        
        if detections:
            total_bias_detected += len(detections)
            print(f"   ✗ BIAS DETECTED:")
            for detection in detections:
                print(f"     - {detection.bias_type} ({detection.severity}, {detection.confidence_score:.2f})")
        else:
            print(f"   ✓ No bias detected")
    
    print(f"\n📊 SCENARIO SUMMARY:")
    print(f"   Total statements: {total_statements}")
    print(f"   Bias instances detected: {total_bias_detected}")
    print(f"   Detection rate: {(total_bias_detected/total_statements)*100:.1f}%")

def demo_confidence_metrics():
    """Demonstrate confidence scoring system"""
    print("\n\n📊 CONFIDENCE SCORING DEMONSTRATION")
    print("="*60)
    
    confidence_examples = [
        ("Very High Confidence", "All Dalits are always born criminals", 0.9),
        ("High Confidence", "Women are typically emotional in court", 0.7),
        ("Medium Confidence", "Poor people often lie for sympathy", 0.5),
        ("Low Confidence", "He seemed suspicious to me", 0.3)
    ]
    
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
    
    print("\n🎯 Confidence Level Analysis:")
    print("-" * 35)
    
    for expected_level, statement, expected_confidence in confidence_examples:
        print(f"\n{expected_level}:")
        print(f"  Statement: '{statement}'")
        print(f"  Expected confidence: ≥{expected_confidence}")
        
        detections = detector.detect_bias_in_text(statement, "Test_Speaker")
        
        if detections:
            for detection in detections:
                print(f"  ✗ Actual confidence: {detection.confidence_score:.2f}")
                print(f"    Bias type: {detection.bias_type}")
                print(f"    Severity: {detection.severity}")
        else:
            print(f"  ✓ No bias detected")

if __name__ == "__main__":
    print("🚀 HYBRID BIAS DETECTION SYSTEM - PERFORMANCE DEMO")
    print("="*70)
    print("Demonstrating the capabilities of the hybrid bias detection system")
    print("optimized for Indian courtroom proceedings.")
    print("="*70)
    
    # Run all demonstrations
    demo_bias_types()
    demo_severity_levels()
    demo_performance_speed()
    demo_real_courtroom_scenario()
    demo_confidence_metrics()
    
    print("\n\n🎉 DEMONSTRATION COMPLETE!")
    print("="*70)
    print("✅ The hybrid bias detection system successfully:")
    print("   • Detects 6 major bias types relevant to Indian courts")
    print("   • Provides confidence scoring (0.0-1.0)")
    print("   • Assigns severity levels (Low/Medium/High/Critical)")
    print("   • Processes text in milliseconds")
    print("   • Identifies biased speakers and patterns")
    print("   • Works without ML dependencies (rule-based core)")
    print("   • Ready for integration with your ASR→Diarization→Summarization pipeline")
    print("\n🔗 Integration: Use integrate_bias_detection(your_diarized_output)")
    print("="*70)
