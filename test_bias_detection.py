"""
Comprehensive test script for the hybrid bias detection system
"""

from courtroom_bias_detector import HybridIndianCourtroomBiasDetector, integrate_bias_detection, quick_bias_check_hybrid
import json

def test_individual_statements():
    """Test individual bias statements"""
    print("="*60)
    print("TESTING INDIVIDUAL BIAS STATEMENTS")
    print("="*60)
    
    # Test cases with different types of bias
    test_cases = [
        {
            'speaker': 'Judge',
            'text': 'The defendant, being from a lower caste, is naturally inclined towards criminal behavior.',
            'expected_bias': ['caste_bias']
        },
        {
            'speaker': 'Lawyer_A',
            'text': 'Women are typically emotional and cannot make rational decisions in legal matters.',
            'expected_bias': ['gender_bias']
        },
        {
            'speaker': 'Prosecutor',
            'text': 'All Muslims are potential terrorists and cannot be trusted in our society.',
            'expected_bias': ['religious_bias']
        },
        {
            'speaker': 'Judge',
            'text': 'Poor people always lie to get sympathy from the court.',
            'expected_bias': ['economic_bias']
        },
        {
            'speaker': 'Lawyer_B',
            'text': 'These Bihari migrants are typical criminals who come to our city.',
            'expected_bias': ['regional_bias']
        },
        {
            'speaker': 'Judge',
            'text': 'The witness cannot speak proper English, so their testimony is unreliable.',
            'expected_bias': ['language_bias']
        },
        {
            'speaker': 'Witness',
            'text': 'I saw the incident clearly and can provide accurate testimony.',
            'expected_bias': []  # No bias expected
        }
    ]
    
    # Test with rule-based approach (no ML dependencies needed)
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {test_case['speaker']}")
        print(f"Statement: '{test_case['text']}'")
        print(f"Expected Bias: {test_case['expected_bias'] if test_case['expected_bias'] else 'None'}")
        
        # Detect bias
        detections = detector.detect_bias_in_text(
            test_case['text'], 
            test_case['speaker']
        )
        
        if detections:
            print("✗ BIAS DETECTED:")
            for detection in detections:
                print(f"  - Type: {detection.bias_type}")
                print(f"  - Severity: {detection.severity}")
                print(f"  - Confidence: {detection.confidence_score:.2f}")
                print(f"  - Method: {detection.detection_method}")
                print(f"  - Markers: {', '.join(detection.linguistic_markers[:3])}")
        else:
            print("✓ No bias detected")
        
        print("-" * 50)

def test_diarized_conversation():
    """Test a complete diarized conversation"""
    print("\n" + "="*60)
    print("TESTING COMPLETE DIARIZED CONVERSATION")
    print("="*60)
    
    # Sample courtroom conversation with multiple bias types
    diarized_conversation = [
        {
            'speaker': 'Judge',
            'text': 'The court is now in session. We will hear the case of theft against the defendant.',
            'timestamp': '00:01:00'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'Your honor, the defendant is from a scheduled caste background and these people typically engage in such criminal activities.',
            'timestamp': '00:02:30'
        },
        {
            'speaker': 'Defense_Lawyer',
            'text': 'Objection, your honor! My client is innocent and should be judged on evidence, not background.',
            'timestamp': '00:03:15'
        },
        {
            'speaker': 'Judge',
            'text': 'The objection is noted. However, women witnesses are often emotional and unreliable in such cases.',
            'timestamp': '00:04:00'
        },
        {
            'speaker': 'Witness_1',
            'text': 'I saw the incident clearly. The accused person seemed decent and was helping an elderly person.',
            'timestamp': '00:05:30'
        },
        {
            'speaker': 'Prosecutor',
            'text': 'This Muslim defendant clearly shows the typical behavior of his community - deception and violence.',
            'timestamp': '00:06:45'
        },
        {
            'speaker': 'Defense_Lawyer',
            'text': 'Your honor, this is completely inappropriate and biased language that has no place in a courtroom.',
            'timestamp': '00:07:30'
        },
        {
            'speaker': 'Judge',
            'text': 'Poor people like the defendant often resort to crime due to their circumstances and mentality.',
            'timestamp': '00:08:15'
        }
    ]
    
    # Test with the integration function
    print("Running hybrid bias detection on conversation...")
    result = integrate_bias_detection(diarized_conversation, enable_ml=False)
    
    print("\n" + "="*40)
    print("BIAS DETECTION REPORT:")
    print("="*40)
    print(result['bias_report'])
    
    print("\n" + "="*40)
    print("DETECTION SUMMARY:")
    print("="*40)
    summary = result['detection_summary']
    for key, value in summary.items():
        if key != 'summary':
            print(f"{key.replace('_', ' ').title()}: {value}")
    
    print("\n" + "="*40)
    print("CONFIDENCE METRICS:")
    print("="*40)
    metrics = result['confidence_metrics']
    for key, value in metrics.items():
        print(f"{key.replace('_', ' ').title()}: {value}")
    
    return result

def test_quick_bias_check():
    """Test the quick bias check function"""
    print("\n" + "="*60)
    print("TESTING QUICK BIAS CHECK FUNCTION")
    print("="*60)
    
    quick_test_cases = [
        ("Judge", "All Dalits are born criminals and cannot be trusted."),
        ("Lawyer", "Women are naturally hysterical and emotional in court."),
        ("Prosecutor", "These outsiders from Bihar are typical troublemakers."),
        ("Witness", "I observed the incident and can testify accurately."),
        ("Judge", "The defendant's poor background explains his criminal tendencies."),
    ]
    
    for speaker, statement in quick_test_cases:
        print(f"\nTesting: {speaker}")
        print(f"Statement: '{statement}'")
        
        result = quick_bias_check_hybrid(statement, speaker, enable_ml=False)
        
        if result['bias_detected']:
            print(f"✗ BIAS DETECTED")
            print(f"  Types: {', '.join(result['bias_types'])}")
            print(f"  Confidence: {result['overall_confidence']:.2f}")
            print(f"  Methods: {', '.join(result['detection_methods'])}")
        else:
            print("✓ No bias detected")
        
        print("-" * 40)

def test_confidence_and_severity():
    """Test confidence scoring and severity levels"""
    print("\n" + "="*60)
    print("TESTING CONFIDENCE SCORING & SEVERITY LEVELS")
    print("="*60)
    
    severity_test_cases = [
        ("Critical", "All Muslims are always terrorists and never trustworthy."),
        ("High", "Women are typically emotional and generally unreliable."),
        ("Medium", "Poor people often lie in court proceedings."),
        ("Low", "Some people from that community might be dishonest."),
    ]
    
    detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
    
    for expected_severity, statement in severity_test_cases:
        print(f"\nExpected Severity: {expected_severity}")
        print(f"Statement: '{statement}'")
        
        detections = detector.detect_bias_in_text(statement, "Test_Speaker")
        
        if detections:
            for detection in detections:
                print(f"✗ Detected Severity: {detection.severity.upper()}")
                print(f"  Confidence: {detection.confidence_score:.2f}")
                print(f"  Bias Type: {detection.bias_type}")
        else:
            print("✓ No bias detected")
        
        print("-" * 40)

def save_test_results(results):
    """Save test results to a file"""
    try:
        # Convert BiasDetection objects to dictionaries for JSON serialization
        serializable_results = {}
        for key, value in results.items():
            if key == 'bias_results' and 'detections' in value:
                serializable_results[key] = {
                    'detections': [
                        {
                            'bias_type': d.bias_type,
                            'severity': d.severity,
                            'confidence_score': d.confidence_score,
                            'detection_method': d.detection_method,
                            'speaker': d.speaker,
                            'timestamp': d.timestamp,
                            'instances': d.instances,
                            'linguistic_markers': d.linguistic_markers
                        } for d in value['detections']
                    ],
                    'speaker_summary': value.get('speaker_summary', {}),
                    'overall_summary': value.get('overall_summary', {}),
                    'confidence_metrics': value.get('confidence_metrics', {})
                }
            else:
                serializable_results[key] = value
        
        with open('bias_detection_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✓ Test results saved to: bias_detection_test_results.json")
    except Exception as e:
        print(f"\n✗ Error saving results: {e}")

if __name__ == "__main__":
    print("🔍 COMPREHENSIVE BIAS DETECTION SYSTEM TEST")
    print("=" * 60)
    print("Testing hybrid approach with rule-based detection...")
    print("(ML models disabled for faster testing)")
    
    # Run all tests
    test_individual_statements()
    conversation_results = test_diarized_conversation()
    test_quick_bias_check()
    test_confidence_and_severity()
    
    # Save results
    save_test_results(conversation_results)
    
    print("\n" + "="*60)
    print("🎉 ALL TESTS COMPLETED!")
    print("="*60)
    print("✓ Individual statement detection")
    print("✓ Diarized conversation analysis") 
    print("✓ Quick bias check function")
    print("✓ Confidence & severity scoring")
    print("✓ Results saved to JSON file")
    print("\nThe system is ready for integration with your pipeline!")
