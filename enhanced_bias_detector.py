"""
Enhanced Bias Detection System - Addressing Critical Limitations
Specifically tackles: Novel Bias Expressions, Context Understanding, Cultural Context, Training Data Bias
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass
from collections import defaultdict, Counter
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

@dataclass
class EnhancedBiasDetection:
    bias_type: str
    severity: str
    confidence_score: float
    detection_method: str
    context_analysis: Dict
    uncertainty_score: float  # NEW: Uncertainty quantification
    explanation: str  # NEW: Detailed explanation
    cultural_context: str = ""
    speaker: str = None
    timestamp: str = None
    evidence_text: str = ""
    alternative_interpretations: List[str] = None  # NEW: Alternative explanations

    def __post_init__(self):
        if self.alternative_interpretations is None:
            self.alternative_interpretations = []

class EnhancedBiasDetector:
    def __init__(self):
        # Initialize NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        
        # ADDRESSING CRITICAL LIMITATION 1: Novel Bias Expression Detection
        self.novel_bias_detectors = {
            'coded_language_patterns': {
                # Indirect identity references
                'urban_coded': ['urban mindset', 'inner city mentality', 'street smart', 'hood culture'],
                'class_coded': ['that type of person', 'people like that', 'their kind', 'those families'],
                'education_coded': ['limited understanding', 'simple minded', 'basic education', 'village mentality'],
                'cultural_coded': ['different values', 'alternative lifestyle', 'unconventional background', 'foreign influence']
            },
            'euphemism_patterns': {
                'identity_euphemisms': ['culturally different', 'diverse background', 'non-traditional', 'alternative community'],
                'capability_euphemisms': ['differently abled', 'challenged', 'limited capacity', 'special needs'],
                'economic_euphemisms': ['economically disadvantaged', 'resource constrained', 'financially challenged']
            },
            'dog_whistle_patterns': {
                'political_dogwhistles': ['law and order', 'family values', 'traditional culture', 'our way of life'],
                'social_dogwhistles': ['proper upbringing', 'good families', 'decent people', 'respectable community'],
                'cultural_dogwhistles': ['real Indians', 'authentic culture', 'pure traditions', 'original values']
            }
        }
        
        # ADDRESSING CRITICAL LIMITATION 2: Context Understanding
        self.context_analyzers = {
            'legal_context_detector': self._create_legal_context_detector(),
            'case_context_analyzer': self._create_case_context_analyzer(),
            'speaker_role_analyzer': self._create_speaker_role_analyzer(),
            'temporal_context_tracker': self._create_temporal_context_tracker()
        }
        
        # Legal terminology whitelist with context
        self.legal_terminology = {
            'sentencing_context': {
                'legitimate': ['criminal background', 'prior convictions', 'rehabilitation potential', 'community ties'],
                'bias_indicators': ['typical criminal', 'born criminal', 'criminal nature', 'criminal mentality']
            },
            'evidence_context': {
                'legitimate': ['witness credibility', 'testimony reliability', 'evidence evaluation'],
                'bias_indicators': ['unreliable witness', 'typical testimony', 'expected behavior']
            },
            'procedural_context': {
                'legitimate': ['case circumstances', 'relevant factors', 'mitigating circumstances'],
                'bias_indicators': ['obvious guilt', 'typical case', 'expected outcome']
            }
        }
        
        # ADDRESSING CRITICAL LIMITATION 3: Cultural Context Understanding
        self.cultural_context_database = {
            'regional_patterns': {
                'north_indian': {
                    'neutral_terms': ['punjabi', 'haryanvi', 'up resident', 'delhi native'],
                    'bias_terms': ['bhaiya', 'up wala', 'typical punjabi', 'delhi mentality'],
                    'cultural_context': 'Regional identity references'
                },
                'south_indian': {
                    'neutral_terms': ['tamil', 'telugu', 'malayali', 'kannada'],
                    'bias_terms': ['madrasi', 'south indian mentality', 'typical south indian'],
                    'cultural_context': 'South Indian stereotyping'
                },
                'eastern_indian': {
                    'neutral_terms': ['bengali', 'assamese', 'odia'],
                    'bias_terms': ['typical bengali', 'fish eater', 'intellectual type'],
                    'cultural_context': 'Eastern regional bias'
                }
            },
            'caste_coded_language': {
                'indirect_references': {
                    'neutral': ['family background', 'ancestral profession', 'traditional occupation'],
                    'biased': ['his people', 'that community', 'their background', 'family lineage'],
                    'context': 'Caste-based assumptions'
                },
                'economic_proxies': {
                    'neutral': ['residential area', 'neighborhood', 'locality'],
                    'biased': ['area indicates', 'address suggests', 'locality shows'],
                    'context': 'Economic status assumptions'
                }
            },
            'religious_patterns': {
                'neutral_references': ['religious practices', 'faith community', 'spiritual beliefs'],
                'biased_references': ['their religion teaches', 'typical of their faith', 'religious influence'],
                'context_markers': ['prayer time', 'religious calendar', 'community customs']
            }
        }
        
        # ADDRESSING CRITICAL LIMITATION 4: Training Data Bias Mitigation
        self.bias_mitigation_strategies = {
            'underrepresented_groups': {
                'tribal_communities': ['adivasi', 'tribal', 'indigenous', 'forest dweller'],
                'lgbtq_references': ['alternative lifestyle', 'different orientation', 'non-traditional'],
                'disability_references': ['differently abled', 'special needs', 'challenged'],
                'age_based': ['too young', 'too old', 'generational difference']
            },
            'intersectionality_patterns': {
                'gender_religion': ['muslim women', 'hindu women', 'christian women'],
                'caste_gender': ['dalit women', 'upper caste women', 'tribal women'],
                'class_religion': ['poor muslims', 'rich hindus', 'middle class christians'],
                'age_caste': ['young dalits', 'old brahmins', 'elderly tribals']
            }
        }
        
        # Enhanced confidence calibration
        self.confidence_calibrator = self._create_confidence_calibrator()
        
        # Uncertainty quantification
        self.uncertainty_estimator = self._create_uncertainty_estimator()

    def enhanced_bias_detection(self, text: str, speaker: str = None, timestamp: str = None,
                               context_history: List[str] = None, case_context: Dict = None) -> List[EnhancedBiasDetection]:
        """
        Enhanced bias detection addressing critical limitations
        """
        detections = []
        
        # Step 1: Novel bias expression detection
        novel_detections = self._detect_novel_bias_expressions(text, speaker, timestamp)
        detections.extend(novel_detections)
        
        # Step 2: Context-aware detection
        context_detections = self._context_aware_detection(text, speaker, timestamp, context_history, case_context)
        detections.extend(context_detections)
        
        # Step 3: Cultural context analysis
        cultural_detections = self._cultural_context_detection(text, speaker, timestamp)
        detections.extend(cultural_detections)
        
        # Step 4: Training bias mitigation
        mitigated_detections = self._apply_bias_mitigation(detections, text)
        
        # Step 5: Enhanced confidence calibration
        calibrated_detections = self._calibrate_confidence(mitigated_detections, text)
        
        # Step 6: Uncertainty quantification
        final_detections = self._quantify_uncertainty(calibrated_detections, text)
        
        return final_detections

    def _detect_novel_bias_expressions(self, text: str, speaker: str, timestamp: str) -> List[EnhancedBiasDetection]:
        """SOLUTION 1: Detect novel and coded bias expressions"""
        detections = []
        text_lower = text.lower()
        
        # Detect coded language patterns
        for category, patterns in self.novel_bias_detectors['coded_language_patterns'].items():
            for pattern in patterns:
                if pattern.lower() in text_lower:
                    confidence = 0.7  # Medium confidence for coded language
                    uncertainty = 0.3  # Higher uncertainty for novel patterns
                    
                    detection = EnhancedBiasDetection(
                        bias_type=f'coded_{category}',
                        severity='medium',
                        confidence_score=confidence,
                        detection_method='novel_expression_detection',
                        context_analysis={'pattern_type': 'coded_language', 'category': category},
                        uncertainty_score=uncertainty,
                        explanation=f"Detected coded language pattern: '{pattern}' which may indicate indirect bias",
                        speaker=speaker,
                        timestamp=timestamp,
                        evidence_text=text,
                        alternative_interpretations=[
                            "Could be legitimate descriptive language",
                            "May be cultural reference without bias intent",
                            "Possible coded bias expression"
                        ]
                    )
                    detections.append(detection)
        
        # Detect euphemisms
        for category, euphemisms in self.novel_bias_detectors['euphemism_patterns'].items():
            for euphemism in euphemisms:
                if euphemism.lower() in text_lower:
                    confidence = 0.6  # Lower confidence for euphemisms
                    uncertainty = 0.4
                    
                    detection = EnhancedBiasDetection(
                        bias_type=f'euphemistic_{category}',
                        severity='low',
                        confidence_score=confidence,
                        detection_method='euphemism_detection',
                        context_analysis={'pattern_type': 'euphemism', 'category': category},
                        uncertainty_score=uncertainty,
                        explanation=f"Detected potential euphemistic bias: '{euphemism}'",
                        speaker=speaker,
                        timestamp=timestamp,
                        evidence_text=text,
                        alternative_interpretations=[
                            "May be politically correct language",
                            "Could indicate underlying bias assumptions",
                            "Possible neutral descriptive term"
                        ]
                    )
                    detections.append(detection)
        
        # Detect dog whistles
        for category, whistles in self.novel_bias_detectors['dog_whistle_patterns'].items():
            for whistle in whistles:
                if whistle.lower() in text_lower:
                    # Dog whistles require context analysis
                    context_score = self._analyze_dog_whistle_context(text, whistle)
                    confidence = 0.5 + (context_score * 0.3)  # Context-dependent confidence
                    uncertainty = 0.5 - (context_score * 0.2)
                    
                    detection = EnhancedBiasDetection(
                        bias_type=f'dog_whistle_{category}',
                        severity='medium' if context_score > 0.5 else 'low',
                        confidence_score=confidence,
                        detection_method='dog_whistle_detection',
                        context_analysis={'pattern_type': 'dog_whistle', 'context_score': context_score},
                        uncertainty_score=uncertainty,
                        explanation=f"Detected potential dog whistle: '{whistle}' (context-dependent)",
                        speaker=speaker,
                        timestamp=timestamp,
                        evidence_text=text,
                        alternative_interpretations=[
                            "May be legitimate political/social reference",
                            "Could be coded bias expression",
                            "Possible neutral cultural reference"
                        ]
                    )
                    detections.append(detection)
        
        return detections

    def _context_aware_detection(self, text: str, speaker: str, timestamp: str,
                                context_history: List[str], case_context: Dict) -> List[EnhancedBiasDetection]:
        """SOLUTION 2: Context-aware bias detection to reduce false positives"""
        detections = []
        
        # Analyze legal context
        legal_context = self._analyze_legal_context(text, case_context)
        
        # Check if potentially biased terms are used in legitimate legal context
        for context_type, terms in self.legal_terminology.items():
            for term in terms['legitimate']:
                if term.lower() in text.lower():
                    # Check if used in biased way despite being legitimate term
                    bias_indicators = terms.get('bias_indicators', [])
                    surrounding_context = self._extract_surrounding_context(text, term)
                    
                    is_biased_usage = any(indicator in surrounding_context.lower() 
                                        for indicator in bias_indicators)
                    
                    if is_biased_usage:
                        confidence = 0.8  # High confidence when legitimate term used in biased way
                        uncertainty = 0.2
                        
                        detection = EnhancedBiasDetection(
                            bias_type='contextual_bias',
                            severity='high',
                            confidence_score=confidence,
                            detection_method='context_aware_detection',
                            context_analysis={
                                'legal_context': context_type,
                                'legitimate_term': term,
                                'biased_usage': True
                            },
                            uncertainty_score=uncertainty,
                            explanation=f"Legitimate legal term '{term}' used in potentially biased context",
                            speaker=speaker,
                            timestamp=timestamp,
                            evidence_text=text,
                            alternative_interpretations=[
                                "Standard legal terminology usage",
                                "Biased application of legal concept",
                                "Context-dependent interpretation needed"
                            ]
                        )
                        detections.append(detection)
        
        return detections

    def _cultural_context_detection(self, text: str, speaker: str, timestamp: str) -> List[EnhancedBiasDetection]:
        """SOLUTION 3: Enhanced cultural context understanding"""
        detections = []
        text_lower = text.lower()
        
        # Analyze regional patterns
        for region, patterns in self.cultural_context_database['regional_patterns'].items():
            # Check for biased regional terms
            for bias_term in patterns['bias_terms']:
                if bias_term.lower() in text_lower:
                    confidence = 0.8  # High confidence for known regional bias terms
                    uncertainty = 0.2
                    
                    detection = EnhancedBiasDetection(
                        bias_type='regional_bias',
                        severity='medium',
                        confidence_score=confidence,
                        detection_method='cultural_context_detection',
                        context_analysis={
                            'region': region,
                            'cultural_context': patterns['cultural_context']
                        },
                        uncertainty_score=uncertainty,
                        explanation=f"Detected regional bias term: '{bias_term}' targeting {region}",
                        cultural_context=patterns['cultural_context'],
                        speaker=speaker,
                        timestamp=timestamp,
                        evidence_text=text,
                        alternative_interpretations=[
                            "Neutral regional reference",
                            "Regional stereotyping",
                            "Cultural bias expression"
                        ]
                    )
                    detections.append(detection)
        
        # Analyze caste-coded language
        caste_patterns = self.cultural_context_database['caste_coded_language']
        for category, patterns in caste_patterns.items():
            for bias_term in patterns['biased']:
                if bias_term.lower() in text_lower:
                    confidence = 0.7  # Medium-high confidence for caste coding
                    uncertainty = 0.3
                    
                    detection = EnhancedBiasDetection(
                        bias_type='caste_coded_bias',
                        severity='medium',
                        confidence_score=confidence,
                        detection_method='cultural_context_detection',
                        context_analysis={
                            'coding_type': category,
                            'cultural_context': patterns['context']
                        },
                        uncertainty_score=uncertainty,
                        explanation=f"Detected caste-coded language: '{bias_term}'",
                        cultural_context=patterns['context'],
                        speaker=speaker,
                        timestamp=timestamp,
                        evidence_text=text,
                        alternative_interpretations=[
                            "Neutral background reference",
                            "Coded caste bias",
                            "Socioeconomic reference"
                        ]
                    )
                    detections.append(detection)
        
        return detections

    def _apply_bias_mitigation(self, detections: List[EnhancedBiasDetection], text: str) -> List[EnhancedBiasDetection]:
        """SOLUTION 4: Mitigate training data bias by enhancing detection for underrepresented groups"""
        
        # Check for underrepresented group bias
        for group, terms in self.bias_mitigation_strategies['underrepresented_groups'].items():
            for term in terms:
                if term.lower() in text.lower():
                    # Boost detection for underrepresented groups
                    enhanced_detection = EnhancedBiasDetection(
                        bias_type=f'{group}_bias',
                        severity='medium',
                        confidence_score=0.7,
                        detection_method='bias_mitigation_enhancement',
                        context_analysis={'underrepresented_group': group},
                        uncertainty_score=0.3,
                        explanation=f"Enhanced detection for underrepresented group: {group}",
                        evidence_text=text,
                        alternative_interpretations=[
                            "Neutral reference to group",
                            "Potential bias against underrepresented group",
                            "Requires cultural sensitivity review"
                        ]
                    )
                    detections.append(enhanced_detection)
        
        # Check for intersectionality patterns
        for pattern_type, patterns in self.bias_mitigation_strategies['intersectionality_patterns'].items():
            for pattern in patterns:
                if pattern.lower() in text.lower():
                    # Boost confidence for intersectional bias
                    intersectional_detection = EnhancedBiasDetection(
                        bias_type='intersectional_bias',
                        severity='high',  # Intersectional bias is often more severe
                        confidence_score=0.8,
                        detection_method='intersectionality_detection',
                        context_analysis={'intersectionality_type': pattern_type, 'pattern': pattern},
                        uncertainty_score=0.2,
                        explanation=f"Detected intersectional bias pattern: '{pattern}'",
                        evidence_text=text,
                        alternative_interpretations=[
                            "Descriptive demographic reference",
                            "Intersectional bias targeting multiple identities",
                            "Compound stereotyping"
                        ]
                    )
                    detections.append(intersectional_detection)
        
        return detections

    # Helper methods for the enhanced detection system
    def _analyze_dog_whistle_context(self, text: str, whistle: str) -> float:
        """Analyze context to determine if dog whistle is being used in biased way"""
        # Simple heuristic - can be enhanced with more sophisticated analysis
        negative_context_words = ['problem', 'issue', 'concern', 'threat', 'danger', 'wrong']
        positive_context_words = ['support', 'promote', 'protect', 'value', 'respect']
        
        text_lower = text.lower()
        negative_score = sum(1 for word in negative_context_words if word in text_lower)
        positive_score = sum(1 for word in positive_context_words if word in text_lower)
        
        if negative_score > positive_score:
            return 0.7  # Higher likelihood of biased usage
        elif positive_score > negative_score:
            return 0.3  # Lower likelihood of biased usage
        else:
            return 0.5  # Neutral context

    def _analyze_legal_context(self, text: str, case_context: Dict) -> Dict:
        """Analyze if text is in legitimate legal context"""
        if not case_context:
            return {'is_legal_context': False}
        
        # Simple legal context detection
        legal_indicators = ['court', 'case', 'evidence', 'testimony', 'witness', 'defendant', 'plaintiff']
        legal_score = sum(1 for indicator in legal_indicators if indicator.lower() in text.lower())
        
        return {
            'is_legal_context': legal_score > 0,
            'legal_score': legal_score,
            'case_type': case_context.get('case_type', 'unknown')
        }

    def _extract_surrounding_context(self, text: str, term: str, window: int = 10) -> str:
        """Extract surrounding context around a term"""
        words = text.split()
        term_positions = [i for i, word in enumerate(words) if term.lower() in word.lower()]
        
        if not term_positions:
            return ""
        
        pos = term_positions[0]
        start = max(0, pos - window)
        end = min(len(words), pos + window + 1)
        
        return " ".join(words[start:end])

    def _calibrate_confidence(self, detections: List[EnhancedBiasDetection], text: str) -> List[EnhancedBiasDetection]:
        """Enhanced confidence calibration"""
        for detection in detections:
            # Adjust confidence based on detection method
            if detection.detection_method == 'novel_expression_detection':
                detection.confidence_score *= 0.9  # Slightly reduce for novel patterns
            elif detection.detection_method == 'context_aware_detection':
                detection.confidence_score *= 1.1  # Boost for context-aware
            elif detection.detection_method == 'cultural_context_detection':
                detection.confidence_score *= 1.05  # Slight boost for cultural context
            
            # Ensure confidence stays within bounds
            detection.confidence_score = min(0.95, max(0.1, detection.confidence_score))
        
        return detections

    def _quantify_uncertainty(self, detections: List[EnhancedBiasDetection], text: str) -> List[EnhancedBiasDetection]:
        """Quantify uncertainty for each detection"""
        for detection in detections:
            # Base uncertainty on confidence
            base_uncertainty = 1.0 - detection.confidence_score
            
            # Adjust based on detection method
            if detection.detection_method in ['novel_expression_detection', 'dog_whistle_detection']:
                base_uncertainty += 0.1  # Higher uncertainty for novel/ambiguous patterns
            elif detection.detection_method == 'context_aware_detection':
                base_uncertainty -= 0.1  # Lower uncertainty for context-aware
            
            detection.uncertainty_score = min(0.9, max(0.1, base_uncertainty))
        
        return detections

    # Placeholder methods for context analyzers
    def _create_legal_context_detector(self): return None
    def _create_case_context_analyzer(self): return None
    def _create_speaker_role_analyzer(self): return None
    def _create_temporal_context_tracker(self): return None
    def _create_confidence_calibrator(self): return None
    def _create_uncertainty_estimator(self): return None

# Integration function
def enhanced_bias_detection(diarized_segments: List[Dict], case_context: Dict = None) -> Dict:
    """
    Enhanced bias detection addressing critical limitations
    """
    detector = EnhancedBiasDetector()
    all_detections = []
    context_history = []
    
    for segment in diarized_segments:
        speaker = segment.get('speaker', 'Unknown')
        text = segment.get('text', '')
        timestamp = segment.get('timestamp', '')
        
        # Enhanced detection with context
        detections = detector.enhanced_bias_detection(
            text, speaker, timestamp, context_history, case_context
        )
        all_detections.extend(detections)
        
        # Update context history
        context_history.append(text)
        if len(context_history) > 5:  # Keep last 5 statements for context
            context_history.pop(0)
    
    return {
        'enhanced_detections': all_detections,
        'total_detections': len(all_detections),
        'confidence_distribution': _calculate_confidence_distribution(all_detections),
        'uncertainty_analysis': _calculate_uncertainty_analysis(all_detections),
        'explanation_summary': _generate_explanation_summary(all_detections)
    }

def _calculate_confidence_distribution(detections: List[EnhancedBiasDetection]) -> Dict:
    """Calculate confidence distribution"""
    if not detections:
        return {'high': 0, 'medium': 0, 'low': 0}
    
    high = sum(1 for d in detections if d.confidence_score > 0.7)
    medium = sum(1 for d in detections if 0.4 < d.confidence_score <= 0.7)
    low = sum(1 for d in detections if d.confidence_score <= 0.4)
    
    return {'high': high, 'medium': medium, 'low': low}

def _calculate_uncertainty_analysis(detections: List[EnhancedBiasDetection]) -> Dict:
    """Calculate uncertainty analysis"""
    if not detections:
        return {'average_uncertainty': 0.0, 'high_uncertainty_count': 0}
    
    uncertainties = [d.uncertainty_score for d in detections]
    avg_uncertainty = np.mean(uncertainties)
    high_uncertainty = sum(1 for u in uncertainties if u > 0.6)
    
    return {
        'average_uncertainty': avg_uncertainty,
        'high_uncertainty_count': high_uncertainty,
        'uncertainty_distribution': {
            'low': sum(1 for u in uncertainties if u < 0.3),
            'medium': sum(1 for u in uncertainties if 0.3 <= u <= 0.6),
            'high': sum(1 for u in uncertainties if u > 0.6)
        }
    }

def _generate_explanation_summary(detections: List[EnhancedBiasDetection]) -> List[str]:
    """Generate summary of explanations"""
    explanations = []
    for detection in detections[:5]:  # Top 5 detections
        explanations.append(f"{detection.bias_type}: {detection.explanation}")
    return explanations
