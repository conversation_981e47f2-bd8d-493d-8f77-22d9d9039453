"""
Enhanced Subtle Bias Detection for Real Courtroom Scenarios
Focuses on detecting hidden, implicit, and low-level biases that are more common in practice
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Set
from dataclasses import dataclass
from collections import defaultdict, Counter
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from sklearn.feature_extraction.text import TfidfVectorizer
import warnings
warnings.filterwarnings('ignore')

@dataclass
class SubtleBiasDetection:
    bias_type: str
    subtlety_level: str  # 'implicit', 'coded', 'microaggression', 'systemic'
    confidence_score: float
    linguistic_pattern: str
    context_indicators: List[str]
    speaker: str = None
    timestamp: str = None
    evidence_text: str = ""
    bias_mechanism: str = ""  # How the bias manifests

class SubtleBiasDetector:
    def __init__(self):
        # Initialize NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        
        # Subtle bias patterns - much more nuanced
        self.subtle_bias_patterns = {
            'implicit_gender_bias': {
                'linguistic_markers': {
                    # Subtle language that implies gender stereotypes
                    'emotional_descriptors': ['emotional', 'hysterical', 'irrational', 'dramatic', 'overreacting'],
                    'competence_qualifiers': ['surprisingly competent', 'quite capable', 'actually intelligent'],
                    'appearance_focus': ['well-dressed', 'attractive', 'presentable', 'neat appearance'],
                    'family_references': ['mother of', 'wife of', 'daughter of', 'family woman'],
                    'diminutives': ['girl', 'young lady', 'dear', 'sweetie', 'honey']
                },
                'contextual_patterns': [
                    r'(?:she|the woman|female witness) (?:seems|appears|looks) (?:emotional|upset|distressed)',
                    r'(?:surprisingly|quite|actually) (?:articulate|intelligent|competent) (?:for a|woman|lady)',
                    r'(?:as a (?:mother|wife|woman))',
                    r'(?:her|she) (?:claims|alleges|says) (?:but|however)'
                ]
            },
            
            'implicit_caste_bias': {
                'linguistic_markers': {
                    'coded_language': ['background', 'upbringing', 'family history', 'social status'],
                    'qualification_doubt': ['claims to be', 'says he is', 'allegedly from'],
                    'behavioral_assumptions': ['typical behavior', 'expected pattern', 'usual conduct'],
                    'economic_proxies': ['area of residence', 'neighborhood', 'locality', 'address']
                },
                'contextual_patterns': [
                    r'(?:given his|considering his|from his) (?:background|upbringing|area)',
                    r'(?:people from|those from) (?:that|such|his) (?:area|locality|background)',
                    r'(?:not what you\'d expect|surprising given|unusual for someone)'
                ]
            },
            
            'implicit_religious_bias': {
                'linguistic_markers': {
                    'coded_references': ['community', 'people like him', 'his kind', 'that group'],
                    'cultural_assumptions': ['cultural background', 'traditional values', 'belief system'],
                    'name_based_bias': ['with a name like', 'given his name', 'someone called'],
                    'timing_references': ['during ramadan', 'on friday', 'religious obligations']
                },
                'contextual_patterns': [
                    r'(?:his|their) (?:community|people|group) (?:usually|typically|often)',
                    r'(?:with a name like|someone called|given his name)',
                    r'(?:cultural|religious) (?:background|influences|factors)'
                ]
            },
            
            'implicit_economic_bias': {
                'linguistic_markers': {
                    'class_indicators': ['education level', 'speaking style', 'manner of dress', 'way of talking'],
                    'assumption_markers': ['probably can\'t afford', 'likely doesn\'t understand', 'may not know'],
                    'credibility_qualifiers': ['despite his circumstances', 'given his situation', 'considering his status'],
                    'language_bias': ['broken english', 'poor grammar', 'limited vocabulary', 'simple language']
                },
                'contextual_patterns': [
                    r'(?:despite|given|considering) (?:his|her) (?:circumstances|situation|background)',
                    r'(?:probably|likely|may) (?:not|can\'t|doesn\'t) (?:understand|know|afford)',
                    r'(?:simple|basic|limited) (?:understanding|knowledge|education)'
                ]
            },
            
            'microaggressions': {
                'linguistic_markers': {
                    'othering_language': ['you people', 'your kind', 'people like you', 'where are you from'],
                    'competence_surprise': ['you speak well', 'surprisingly articulate', 'better than expected'],
                    'cultural_assumptions': ['is that your real name', 'what\'s your actual name', 'how do you pronounce'],
                    'tokenism': ['you must know about', 'as someone from your community', 'representing your people']
                },
                'contextual_patterns': [
                    r'(?:you|your) (?:people|kind|community|type)',
                    r'(?:surprisingly|quite|actually) (?:well-spoken|articulate|educated)',
                    r'(?:where are you|are you from|originally from)'
                ]
            }
        }
        
        # Subtle bias amplifiers - words that increase bias likelihood
        self.subtlety_amplifiers = {
            'hedging': ['seems', 'appears', 'looks like', 'might be', 'could be', 'possibly'],
            'qualifying': ['but', 'however', 'although', 'despite', 'even though'],
            'distancing': ['allegedly', 'claims', 'says', 'purports', 'maintains'],
            'minimizing': ['just', 'only', 'merely', 'simply', 'basically']
        }
        
        # Context analysis for subtle bias
        self.context_analyzers = {
            'tone_shift': self._analyze_tone_shift,
            'comparative_language': self._analyze_comparative_language,
            'assumption_patterns': self._analyze_assumption_patterns,
            'credibility_markers': self._analyze_credibility_markers
        }

    def detect_subtle_bias(self, text: str, speaker: str = None, timestamp: str = None, 
                          context_history: List[str] = None) -> List[SubtleBiasDetection]:
        """
        Detect subtle and implicit biases in text
        """
        detections = []
        
        # Method 1: Linguistic pattern analysis
        linguistic_detections = self._detect_linguistic_patterns(text, speaker, timestamp)
        detections.extend(linguistic_detections)
        
        # Method 2: Contextual analysis
        contextual_detections = self._detect_contextual_bias(text, speaker, timestamp, context_history)
        detections.extend(contextual_detections)
        
        # Method 3: Comparative analysis (if context available)
        if context_history:
            comparative_detections = self._detect_comparative_bias(text, context_history, speaker, timestamp)
            detections.extend(comparative_detections)
        
        # Method 4: Microaggression detection
        micro_detections = self._detect_microaggressions(text, speaker, timestamp)
        detections.extend(micro_detections)
        
        # Method 5: Systemic pattern analysis
        systemic_detections = self._detect_systemic_patterns(text, speaker, timestamp)
        detections.extend(systemic_detections)
        
        return detections

    def _detect_linguistic_patterns(self, text: str, speaker: str, timestamp: str) -> List[SubtleBiasDetection]:
        """Detect subtle bias through linguistic patterns"""
        detections = []
        text_lower = text.lower()
        
        for bias_type, patterns in self.subtle_bias_patterns.items():
            confidence_scores = []
            evidence_pieces = []
            context_indicators = []
            
            # Check linguistic markers
            for category, markers in patterns['linguistic_markers'].items():
                for marker in markers:
                    if marker.lower() in text_lower:
                        confidence_scores.append(0.6)  # Moderate confidence for subtle patterns
                        evidence_pieces.append(marker)
                        context_indicators.append(f"linguistic_marker: {category}")
            
            # Check contextual patterns
            for pattern in patterns.get('contextual_patterns', []):
                matches = re.finditer(pattern, text_lower, re.IGNORECASE)
                for match in matches:
                    confidence_scores.append(0.7)  # Higher confidence for pattern matches
                    evidence_pieces.append(match.group())
                    context_indicators.append(f"pattern_match: {pattern[:30]}...")
            
            if confidence_scores:
                avg_confidence = np.mean(confidence_scores)
                
                # Apply subtlety amplifiers
                amplifier_boost = self._calculate_amplifier_boost(text_lower)
                final_confidence = min(0.95, avg_confidence + amplifier_boost)
                
                detections.append(SubtleBiasDetection(
                    bias_type=bias_type,
                    subtlety_level=self._determine_subtlety_level(final_confidence, evidence_pieces),
                    confidence_score=final_confidence,
                    linguistic_pattern=', '.join(evidence_pieces[:3]),
                    context_indicators=context_indicators,
                    speaker=speaker,
                    timestamp=timestamp,
                    evidence_text=text,
                    bias_mechanism=self._identify_bias_mechanism(bias_type, evidence_pieces)
                ))
        
        return detections

    def _detect_microaggressions(self, text: str, speaker: str, timestamp: str) -> List[SubtleBiasDetection]:
        """Detect microaggressions - subtle, often unconscious biases"""
        detections = []
        text_lower = text.lower()
        
        microaggression_patterns = self.subtle_bias_patterns['microaggressions']
        
        for category, markers in microaggression_patterns['linguistic_markers'].items():
            for marker in markers:
                if marker.lower() in text_lower:
                    # Microaggressions often have lower confidence but high impact
                    confidence = 0.5 + (0.3 if any(amp in text_lower for amp in self.subtlety_amplifiers['hedging']) else 0)
                    
                    detections.append(SubtleBiasDetection(
                        bias_type='microaggression',
                        subtlety_level='microaggression',
                        confidence_score=confidence,
                        linguistic_pattern=marker,
                        context_indicators=[f"microaggression_type: {category}"],
                        speaker=speaker,
                        timestamp=timestamp,
                        evidence_text=text,
                        bias_mechanism=f"Microaggression through {category}"
                    ))
        
        return detections

    def _detect_comparative_bias(self, text: str, context_history: List[str], 
                                speaker: str, timestamp: str) -> List[SubtleBiasDetection]:
        """Detect bias through comparative analysis with context"""
        detections = []
        
        if not context_history:
            return detections
        
        # Analyze if speaker treats different groups differently
        current_descriptors = self._extract_descriptors(text)
        historical_descriptors = []
        
        for context_text in context_history[-5:]:  # Last 5 statements for context
            historical_descriptors.extend(self._extract_descriptors(context_text))
        
        # Look for differential treatment patterns
        if self._shows_differential_treatment(current_descriptors, historical_descriptors):
            detections.append(SubtleBiasDetection(
                bias_type='differential_treatment',
                subtlety_level='systemic',
                confidence_score=0.6,
                linguistic_pattern='differential_descriptors',
                context_indicators=['comparative_analysis'],
                speaker=speaker,
                timestamp=timestamp,
                evidence_text=text,
                bias_mechanism="Differential treatment based on group membership"
            ))
        
        return detections

    def _calculate_amplifier_boost(self, text: str) -> float:
        """Calculate confidence boost from subtlety amplifiers"""
        boost = 0.0
        
        for category, amplifiers in self.subtlety_amplifiers.items():
            for amplifier in amplifiers:
                if amplifier in text:
                    boost += 0.1  # Small boost for each amplifier
        
        return min(0.3, boost)  # Cap the boost

    def _determine_subtlety_level(self, confidence: float, evidence: List[str]) -> str:
        """Determine the level of subtlety"""
        if confidence > 0.8:
            return 'coded'  # Coded language
        elif confidence > 0.6:
            return 'implicit'  # Implicit bias
        elif any('micro' in str(e).lower() for e in evidence):
            return 'microaggression'
        else:
            return 'systemic'  # Systemic patterns

    def _identify_bias_mechanism(self, bias_type: str, evidence: List[str]) -> str:
        """Identify how the bias manifests"""
        mechanisms = {
            'implicit_gender_bias': 'Gender stereotyping through language',
            'implicit_caste_bias': 'Caste assumptions through coded language',
            'implicit_religious_bias': 'Religious profiling through community references',
            'implicit_economic_bias': 'Class bias through competence assumptions',
            'microaggressions': 'Subtle othering and marginalization'
        }
        return mechanisms.get(bias_type, 'Subtle bias through linguistic patterns')

    def _extract_descriptors(self, text: str) -> List[str]:
        """Extract descriptive words and phrases"""
        # Simple descriptor extraction - can be enhanced
        descriptive_words = []
        words = word_tokenize(text.lower())
        
        # Look for adjectives and descriptive phrases
        descriptive_patterns = ['seems', 'appears', 'looks', 'acts', 'behaves', 'sounds']
        
        for i, word in enumerate(words):
            if word in descriptive_patterns and i + 1 < len(words):
                descriptive_words.append(f"{word} {words[i+1]}")
        
        return descriptive_words

    def _shows_differential_treatment(self, current: List[str], historical: List[str]) -> bool:
        """Check if there's differential treatment in language"""
        # Simple heuristic - can be enhanced with more sophisticated analysis
        if not current or not historical:
            return False
        
        current_tone = len([d for d in current if any(neg in d for neg in ['negative', 'suspicious', 'doubtful'])])
        historical_tone = len([d for d in historical if any(neg in d for neg in ['negative', 'suspicious', 'doubtful'])])
        
        # If current tone is significantly more negative
        return current_tone > 0 and (current_tone / len(current)) > 1.5 * (historical_tone / max(1, len(historical)))

    def _analyze_tone_shift(self, text: str) -> float:
        """Analyze tone shifts that might indicate bias"""
        # Placeholder for tone analysis
        return 0.0

    def _analyze_comparative_language(self, text: str) -> float:
        """Analyze comparative language patterns"""
        # Placeholder for comparative analysis
        return 0.0

    def _analyze_assumption_patterns(self, text: str) -> float:
        """Analyze assumption-making patterns"""
        # Placeholder for assumption analysis
        return 0.0

    def _analyze_credibility_markers(self, text: str) -> float:
        """Analyze credibility assessment patterns"""
        # Placeholder for credibility analysis
        return 0.0

    def _detect_contextual_bias(self, text: str, speaker: str, timestamp: str, 
                               context_history: List[str]) -> List[SubtleBiasDetection]:
        """Detect bias through contextual analysis"""
        # Placeholder for contextual bias detection
        return []

    def _detect_systemic_patterns(self, text: str, speaker: str, timestamp: str) -> List[SubtleBiasDetection]:
        """Detect systemic bias patterns"""
        # Placeholder for systemic pattern detection
        return []

# Integration function for subtle bias detection
def detect_subtle_bias_in_conversation(diarized_segments: List[Dict], 
                                     context_window: int = 5) -> Dict:
    """
    Detect subtle bias in a complete conversation with context awareness
    """
    detector = SubtleBiasDetector()
    all_detections = []
    context_history = []
    
    for segment in diarized_segments:
        speaker = segment.get('speaker', 'Unknown')
        text = segment.get('text', '')
        timestamp = segment.get('timestamp', '')
        
        # Detect subtle bias with context
        detections = detector.detect_subtle_bias(text, speaker, timestamp, context_history)
        all_detections.extend(detections)
        
        # Update context history
        context_history.append(text)
        if len(context_history) > context_window:
            context_history.pop(0)
    
    return {
        'subtle_detections': all_detections,
        'detection_summary': _generate_subtle_bias_summary(all_detections),
        'risk_assessment': _assess_subtle_bias_risk(all_detections)
    }

def _generate_subtle_bias_summary(detections: List[SubtleBiasDetection]) -> Dict:
    """Generate summary of subtle bias detections"""
    if not detections:
        return {'total_detections': 0, 'risk_level': 'low'}
    
    bias_types = Counter(d.bias_type for d in detections)
    subtlety_levels = Counter(d.subtlety_level for d in detections)
    speakers = Counter(d.speaker for d in detections)
    
    return {
        'total_detections': len(detections),
        'bias_types': dict(bias_types),
        'subtlety_levels': dict(subtlety_levels),
        'affected_speakers': dict(speakers),
        'average_confidence': np.mean([d.confidence_score for d in detections]),
        'risk_level': _calculate_risk_level(detections)
    }

def _assess_subtle_bias_risk(detections: List[SubtleBiasDetection]) -> Dict:
    """Assess the risk level of detected subtle biases"""
    if not detections:
        return {'overall_risk': 'low', 'recommendations': ['Continue monitoring']}
    
    high_risk_count = sum(1 for d in detections if d.confidence_score > 0.7)
    microaggression_count = sum(1 for d in detections if d.subtlety_level == 'microaggression')
    
    risk_level = 'low'
    if high_risk_count > 2 or microaggression_count > 3:
        risk_level = 'high'
    elif high_risk_count > 0 or microaggression_count > 1:
        risk_level = 'medium'
    
    recommendations = _generate_recommendations(risk_level, detections)
    
    return {
        'overall_risk': risk_level,
        'high_confidence_detections': high_risk_count,
        'microaggression_count': microaggression_count,
        'recommendations': recommendations
    }

def _calculate_risk_level(detections: List[SubtleBiasDetection]) -> str:
    """Calculate overall risk level"""
    if not detections:
        return 'low'
    
    avg_confidence = np.mean([d.confidence_score for d in detections])
    detection_count = len(detections)
    
    if avg_confidence > 0.7 and detection_count > 3:
        return 'high'
    elif avg_confidence > 0.5 or detection_count > 2:
        return 'medium'
    else:
        return 'low'

def _generate_recommendations(risk_level: str, detections: List[SubtleBiasDetection]) -> List[str]:
    """Generate recommendations based on risk level"""
    recommendations = []
    
    if risk_level == 'high':
        recommendations.extend([
            'Immediate review of proceedings required',
            'Consider bias training for court personnel',
            'Implement real-time bias monitoring'
        ])
    elif risk_level == 'medium':
        recommendations.extend([
            'Monitor for pattern development',
            'Review speaker bias history',
            'Consider intervention if patterns persist'
        ])
    else:
        recommendations.extend([
            'Continue standard monitoring',
            'Document for trend analysis'
        ])
    
    return recommendations
