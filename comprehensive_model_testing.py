"""
Comprehensive Model Testing - Enhanced Bias Detection System
Testing performance across diverse examples and scenarios
"""

from improved_bias_detector import improved_bias_detection, ImprovedBiasDetector
import json
from typing import Dict, List

def create_comprehensive_test_cases():
    """Create comprehensive test cases across different bias types and scenarios"""
    
    test_cases = {
        'subtle_implicit_bias': [
            {
                'text': 'The witness seems quite articulate for someone from his background.',
                'expected': 'Should detect implicit assumption about background vs articulation',
                'bias_type': 'implicit_assumption',
                'difficulty': 'high'
            },
            {
                'text': 'She\'s surprisingly well-educated given her family circumstances.',
                'expected': 'Should detect assumption about family circumstances vs education',
                'bias_type': 'implicit_assumption',
                'difficulty': 'high'
            },
            {
                'text': 'He speaks English very well despite being from a rural area.',
                'expected': 'Should detect rural vs English proficiency assumption',
                'bias_type': 'geographic_bias',
                'difficulty': 'medium'
            },
            {
                'text': 'The defendant is more cooperative than we usually see from his community.',
                'expected': 'Should detect community-based behavioral assumptions',
                'bias_type': 'community_stereotyping',
                'difficulty': 'medium'
            }
        ],
        
        'intersectional_bias': [
            {
                'text': 'Young Muslim men from poor families often get involved in such activities.',
                'expected': 'Should detect age + religion + economic + gender intersection',
                'bias_type': 'intersectional',
                'difficulty': 'medium'
            },
            {
                'text': 'Elderly Dalit women are usually more submissive in court proceedings.',
                'expected': 'Should detect age + caste + gender intersection',
                'bias_type': 'intersectional',
                'difficulty': 'medium'
            },
            {
                'text': 'Educated upper-caste women typically handle cross-examination better.',
                'expected': 'Should detect education + caste + gender intersection',
                'bias_type': 'intersectional',
                'difficulty': 'high'
            },
            {
                'text': 'Tribal youth from remote areas struggle with urban legal concepts.',
                'expected': 'Should detect ethnicity + age + geography intersection',
                'bias_type': 'intersectional',
                'difficulty': 'high'
            }
        ],
        
        'contextual_ambiguity': [
            {
                'text': 'The defendant\'s criminal history includes three prior convictions for theft.',
                'expected': 'Should recognize legitimate factual statement',
                'bias_type': 'neutral',
                'difficulty': 'low'
            },
            {
                'text': 'Given his criminal history, we can expect this type of behavior to continue.',
                'expected': 'Should detect biased prediction based on history',
                'bias_type': 'predictive_bias',
                'difficulty': 'medium'
            },
            {
                'text': 'Community standards require us to consider the impact on local families.',
                'expected': 'Should recognize legitimate legal consideration',
                'bias_type': 'neutral',
                'difficulty': 'medium'
            },
            {
                'text': 'People from that community don\'t share our community standards.',
                'expected': 'Should detect othering and cultural bias',
                'bias_type': 'cultural_othering',
                'difficulty': 'medium'
            }
        ],
        
        'coded_language': [
            {
                'text': 'The defendant has that inner-city vibe, if you know what I mean.',
                'expected': 'Should detect coded racial/class language',
                'bias_type': 'coded_racial',
                'difficulty': 'medium'
            },
            {
                'text': 'She has a very cosmopolitan outlook, unlike others from her area.',
                'expected': 'Should detect coded class/cultural bias',
                'bias_type': 'coded_class',
                'difficulty': 'high'
            },
            {
                'text': 'The witness displays typical characteristics of his demographic.',
                'expected': 'Should detect demographic stereotyping',
                'bias_type': 'demographic_stereotyping',
                'difficulty': 'medium'
            },
            {
                'text': 'He has adopted a more progressive mindset recently.',
                'expected': 'May be neutral or coded - context dependent',
                'bias_type': 'ambiguous',
                'difficulty': 'high'
            }
        ],
        
        'regional_cultural': [
            {
                'text': 'These Punjabis are always involved in such disputes.',
                'expected': 'Should detect regional stereotyping',
                'bias_type': 'regional_stereotyping',
                'difficulty': 'low'
            },
            {
                'text': 'The witness has that typical Gujarati business mentality.',
                'expected': 'Should detect regional + occupational stereotyping',
                'bias_type': 'regional_occupational',
                'difficulty': 'medium'
            },
            {
                'text': 'Bengali intellectuals often overthink simple legal matters.',
                'expected': 'Should detect regional + intellectual stereotyping',
                'bias_type': 'regional_intellectual',
                'difficulty': 'medium'
            },
            {
                'text': 'People from the Northeast have different cultural values.',
                'expected': 'Should detect regional othering',
                'bias_type': 'regional_othering',
                'difficulty': 'medium'
            }
        ],
        
        'professional_bias': [
            {
                'text': 'As a taxi driver, he probably doesn\'t understand complex legal procedures.',
                'expected': 'Should detect occupational bias about intelligence',
                'bias_type': 'occupational_intelligence',
                'difficulty': 'medium'
            },
            {
                'text': 'Being a domestic worker, she might not be reliable with time commitments.',
                'expected': 'Should detect occupational bias about reliability',
                'bias_type': 'occupational_reliability',
                'difficulty': 'medium'
            },
            {
                'text': 'Street vendors typically have cash-based transactions, making records unclear.',
                'expected': 'Should recognize legitimate occupational observation',
                'bias_type': 'neutral',
                'difficulty': 'high'
            },
            {
                'text': 'Farmers from rural areas often have difficulty with paperwork.',
                'expected': 'Should detect occupational + geographic bias',
                'bias_type': 'occupational_geographic',
                'difficulty': 'medium'
            }
        ],
        
        'gender_bias': [
            {
                'text': 'The female witness seems quite emotional about the incident.',
                'expected': 'Should detect gender stereotype about emotionality',
                'bias_type': 'gender_emotional',
                'difficulty': 'medium'
            },
            {
                'text': 'As a mother, she would naturally want to protect her family.',
                'expected': 'Should detect gender role assumption',
                'bias_type': 'gender_role',
                'difficulty': 'high'
            },
            {
                'text': 'Women from traditional families might not speak up in court.',
                'expected': 'Should detect gender + cultural intersection',
                'bias_type': 'gender_cultural',
                'difficulty': 'medium'
            },
            {
                'text': 'The male defendant appears more aggressive than typical female defendants.',
                'expected': 'Should detect gender-based behavioral assumptions',
                'bias_type': 'gender_behavioral',
                'difficulty': 'medium'
            }
        ],
        
        'economic_bias': [
            {
                'text': 'People from slum areas are more likely to be involved in petty crimes.',
                'expected': 'Should detect economic geography bias',
                'bias_type': 'economic_geographic',
                'difficulty': 'low'
            },
            {
                'text': 'The defendant cannot afford a good lawyer, which explains his situation.',
                'expected': 'Should detect economic bias about legal representation',
                'bias_type': 'economic_legal',
                'difficulty': 'medium'
            },
            {
                'text': 'Wealthy families usually have better character witnesses.',
                'expected': 'Should detect economic bias about character',
                'bias_type': 'economic_character',
                'difficulty': 'medium'
            },
            {
                'text': 'The witness works multiple jobs to support her family.',
                'expected': 'Should recognize neutral economic description',
                'bias_type': 'neutral',
                'difficulty': 'high'
            }
        ],
        
        'religious_bias': [
            {
                'text': 'People of his faith often have different concepts of truth-telling.',
                'expected': 'Should detect religious bias about honesty',
                'bias_type': 'religious_honesty',
                'difficulty': 'medium'
            },
            {
                'text': 'The defendant\'s religious background suggests strong moral values.',
                'expected': 'Should detect positive religious stereotyping',
                'bias_type': 'religious_positive_stereotype',
                'difficulty': 'high'
            },
            {
                'text': 'His religious practices might influence his testimony timing.',
                'expected': 'Should recognize legitimate scheduling consideration',
                'bias_type': 'neutral',
                'difficulty': 'high'
            },
            {
                'text': 'Minority religious communities often face discrimination in such cases.',
                'expected': 'Should recognize legitimate discrimination observation',
                'bias_type': 'neutral',
                'difficulty': 'high'
            }
        ],
        
        'age_bias': [
            {
                'text': 'Young people today don\'t respect authority like previous generations.',
                'expected': 'Should detect generational bias',
                'bias_type': 'generational',
                'difficulty': 'medium'
            },
            {
                'text': 'At his age, he should know better than to get involved in such activities.',
                'expected': 'Should detect age-based moral assumptions',
                'bias_type': 'age_moral',
                'difficulty': 'high'
            },
            {
                'text': 'Elderly witnesses sometimes have memory issues that affect testimony.',
                'expected': 'Should recognize legitimate age-related consideration',
                'bias_type': 'neutral',
                'difficulty': 'high'
            },
            {
                'text': 'Teenagers from broken homes often end up in trouble.',
                'expected': 'Should detect age + family structure bias',
                'bias_type': 'age_family',
                'difficulty': 'medium'
            }
        ]
    }
    
    return test_cases

def run_comprehensive_testing():
    """Run comprehensive testing across all categories"""
    
    print("🧪 COMPREHENSIVE MODEL TESTING")
    print("=" * 60)
    print("Testing enhanced bias detection model across diverse scenarios...")
    print("=" * 60)
    
    test_cases = create_comprehensive_test_cases()
    detector = ImprovedBiasDetector()
    
    # Track overall results
    overall_results = {
        'total_cases': 0,
        'detected_cases': 0,
        'missed_cases': 0,
        'false_positives': 0,  # Cases marked as neutral but detected as bias
        'true_positives': 0,   # Cases marked as bias and correctly detected
        'category_performance': {},
        'difficulty_performance': {'low': {'total': 0, 'detected': 0}, 
                                 'medium': {'total': 0, 'detected': 0}, 
                                 'high': {'total': 0, 'detected': 0}},
        'detailed_results': []
    }
    
    for category, cases in test_cases.items():
        print(f"\n📂 TESTING CATEGORY: {category.upper().replace('_', ' ')}")
        print("-" * 50)
        
        category_stats = {'total': 0, 'detected': 0, 'missed': 0, 'false_positives': 0}
        
        for i, case in enumerate(cases, 1):
            print(f"\nTest {i}: {case['text']}")
            print(f"Expected: {case['expected']}")
            print(f"Difficulty: {case['difficulty']} | Expected Type: {case['bias_type']}")
            
            # Run detection
            detections = detector.improved_bias_detection(case['text'], 'Test_Speaker')
            
            # Update counters
            overall_results['total_cases'] += 1
            category_stats['total'] += 1
            overall_results['difficulty_performance'][case['difficulty']]['total'] += 1
            
            # Analyze results
            is_bias_expected = case['bias_type'] != 'neutral'
            bias_detected = len(detections) > 0
            
            if bias_detected:
                print("✅ BIAS DETECTED:")
                overall_results['detected_cases'] += 1
                category_stats['detected'] += 1
                overall_results['difficulty_performance'][case['difficulty']]['detected'] += 1
                
                if is_bias_expected:
                    overall_results['true_positives'] += 1
                else:
                    overall_results['false_positives'] += 1
                    print("⚠️ POTENTIAL FALSE POSITIVE (expected neutral)")
                
                # Show detection details
                for detection in detections[:2]:  # Show top 2
                    print(f"  • Type: {detection.bias_type}")
                    print(f"  • Confidence: {detection.confidence_score:.2f}")
                    print(f"  • Uncertainty: {detection.uncertainty_score:.2f}")
                    print(f"  • Method: {detection.detection_method}")
                
                if len(detections) > 2:
                    print(f"  • ... and {len(detections) - 2} more detections")
            else:
                print("❌ NO BIAS DETECTED")
                overall_results['missed_cases'] += 1
                category_stats['missed'] += 1
                
                if is_bias_expected:
                    print("⚠️ MISSED BIAS (should have been detected)")
            
            # Store detailed result
            overall_results['detailed_results'].append({
                'category': category,
                'text': case['text'],
                'expected_type': case['bias_type'],
                'difficulty': case['difficulty'],
                'detected': bias_detected,
                'detections': len(detections),
                'is_correct': (bias_detected and is_bias_expected) or (not bias_detected and not is_bias_expected)
            })
        
        # Calculate category performance
        detection_rate = (category_stats['detected'] / category_stats['total']) * 100 if category_stats['total'] > 0 else 0
        overall_results['category_performance'][category] = {
            'detection_rate': detection_rate,
            'total': category_stats['total'],
            'detected': category_stats['detected'],
            'missed': category_stats['missed'],
            'false_positives': category_stats['false_positives']
        }
        
        print(f"\n📊 {category.upper()} PERFORMANCE:")
        print(f"   Detection Rate: {detection_rate:.1f}% ({category_stats['detected']}/{category_stats['total']})")
    
    return overall_results

def analyze_model_performance(results):
    """Analyze model performance and identify improvement areas"""
    
    print(f"\n\n📈 MODEL PERFORMANCE ANALYSIS")
    print("=" * 60)
    
    # Overall metrics
    total = results['total_cases']
    detected = results['detected_cases']
    missed = results['missed_cases']
    true_pos = results['true_positives']
    false_pos = results['false_positives']
    
    overall_detection_rate = (detected / total) * 100 if total > 0 else 0
    precision = (true_pos / detected) * 100 if detected > 0 else 0
    
    print(f"\n🎯 OVERALL METRICS:")
    print(f"   Total Cases: {total}")
    print(f"   Detection Rate: {overall_detection_rate:.1f}% ({detected}/{total})")
    print(f"   Precision: {precision:.1f}% ({true_pos}/{detected})")
    print(f"   False Positives: {false_pos}")
    print(f"   Missed Cases: {missed}")
    
    # Performance by difficulty
    print(f"\n📊 PERFORMANCE BY DIFFICULTY:")
    for difficulty, stats in results['difficulty_performance'].items():
        if stats['total'] > 0:
            rate = (stats['detected'] / stats['total']) * 100
            print(f"   {difficulty.upper()}: {rate:.1f}% ({stats['detected']}/{stats['total']})")
    
    # Best and worst performing categories
    category_performance = results['category_performance']
    sorted_categories = sorted(category_performance.items(), key=lambda x: x[1]['detection_rate'])
    
    print(f"\n🏆 BEST PERFORMING CATEGORIES:")
    for category, stats in sorted_categories[-3:]:  # Top 3
        print(f"   {category.replace('_', ' ').title()}: {stats['detection_rate']:.1f}%")
    
    print(f"\n⚠️ NEEDS IMPROVEMENT:")
    for category, stats in sorted_categories[:3]:  # Bottom 3
        print(f"   {category.replace('_', ' ').title()}: {stats['detection_rate']:.1f}%")
    
    # Identify specific improvement areas
    improvement_areas = []
    
    # Categories with low detection rates
    for category, stats in category_performance.items():
        if stats['detection_rate'] < 50:
            improvement_areas.append({
                'area': category,
                'issue': 'Low detection rate',
                'rate': stats['detection_rate'],
                'priority': 'high'
            })
    
    # High false positive categories
    for category, stats in category_performance.items():
        if stats['false_positives'] > 1:
            improvement_areas.append({
                'area': category,
                'issue': 'High false positives',
                'count': stats['false_positives'],
                'priority': 'medium'
            })
    
    return {
        'overall_detection_rate': overall_detection_rate,
        'precision': precision,
        'improvement_areas': improvement_areas,
        'best_categories': sorted_categories[-3:],
        'worst_categories': sorted_categories[:3]
    }

def generate_improvement_recommendations(results, analysis):
    """Generate specific improvement recommendations"""
    
    recommendations = []
    
    # Based on worst performing categories
    for category, stats in analysis['worst_categories']:
        if stats['detection_rate'] < 30:
            recommendations.append({
                'priority': 'high',
                'area': category.replace('_', ' ').title(),
                'issue': f"Very low detection rate ({stats['detection_rate']:.1f}%)",
                'recommendation': f"Enhance pattern recognition for {category} scenarios",
                'technical_approach': get_technical_approach(category)
            })
    
    # Based on difficulty performance
    difficulty_perf = results['difficulty_performance']
    if difficulty_perf['high']['total'] > 0:
        high_diff_rate = (difficulty_perf['high']['detected'] / difficulty_perf['high']['total']) * 100
        if high_diff_rate < 40:
            recommendations.append({
                'priority': 'medium',
                'area': 'High Difficulty Cases',
                'issue': f"Poor performance on complex cases ({high_diff_rate:.1f}%)",
                'recommendation': "Implement advanced semantic analysis for subtle bias",
                'technical_approach': "Add contextual embeddings and semantic relationship analysis"
            })
    
    # Based on false positives
    if results['false_positives'] > 5:
        recommendations.append({
            'priority': 'medium',
            'area': 'False Positive Reduction',
            'issue': f"High false positive rate ({results['false_positives']} cases)",
            'recommendation': "Improve context understanding and neutral case filtering",
            'technical_approach': "Enhance legal context analysis and professional language recognition"
        })
    
    return recommendations

def get_technical_approach(category):
    """Get technical approach for improving specific category"""
    
    approaches = {
        'subtle_implicit_bias': 'Implement assumption detection algorithms and semantic inference',
        'contextual_ambiguity': 'Add advanced context classification and domain knowledge',
        'coded_language': 'Expand coded language database and pattern matching',
        'regional_cultural': 'Enhance cultural context database and regional pattern recognition',
        'professional_bias': 'Add occupational knowledge base and professional context analysis',
        'gender_bias': 'Improve gender role assumption detection and stereotype recognition',
        'economic_bias': 'Enhance socioeconomic context analysis and class bias patterns',
        'religious_bias': 'Add religious context understanding and faith-based bias patterns',
        'age_bias': 'Improve generational bias detection and age-related assumption recognition',
        'intersectional_bias': 'Enhance compound identity recognition and intersectionality scoring'
    }
    
    return approaches.get(category, 'Develop category-specific pattern recognition and context analysis')

def save_comprehensive_results(results, analysis, recommendations):
    """Save comprehensive test results"""
    
    final_results = {
        'test_summary': {
            'total_cases': results['total_cases'],
            'overall_detection_rate': analysis['overall_detection_rate'],
            'precision': analysis['precision'],
            'false_positives': results['false_positives'],
            'categories_tested': len(results['category_performance'])
        },
        'category_performance': results['category_performance'],
        'difficulty_analysis': results['difficulty_performance'],
        'improvement_recommendations': recommendations,
        'detailed_results': results['detailed_results'][:20],  # Sample of detailed results
        'next_steps': [
            'Focus on low-performing categories',
            'Reduce false positive rate',
            'Improve high-difficulty case handling',
            'Enhance context understanding',
            'Expand cultural and regional knowledge'
        ]
    }
    
    try:
        with open('model_performance_results.json', 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Comprehensive results saved to: model_performance_results.json")
    except Exception as e:
        print(f"\n❌ Error saving results: {e}")

def main():
    """Main testing function"""
    
    print("🚀 ENHANCED BIAS DETECTION MODEL - COMPREHENSIVE TESTING")
    print("=" * 70)
    print("Evaluating model performance across diverse bias scenarios...")
    print("=" * 70)
    
    # Run comprehensive testing
    results = run_comprehensive_testing()
    
    # Analyze performance
    analysis = analyze_model_performance(results)
    
    # Generate recommendations
    recommendations = generate_improvement_recommendations(results, analysis)
    
    # Display recommendations
    print(f"\n🔧 IMPROVEMENT RECOMMENDATIONS:")
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. {rec['area']} ({rec['priority']} priority)")
        print(f"   Issue: {rec['issue']}")
        print(f"   Recommendation: {rec['recommendation']}")
        print(f"   Technical Approach: {rec['technical_approach']}")
    
    # Save results
    save_comprehensive_results(results, analysis, recommendations)
    
    # Final summary
    print(f"\n\n🎉 TESTING COMPLETE!")
    print("=" * 70)
    print(f"📊 FINAL PERFORMANCE SUMMARY:")
    print(f"   Overall Detection Rate: {analysis['overall_detection_rate']:.1f}%")
    print(f"   Precision: {analysis['precision']:.1f}%")
    print(f"   Categories Tested: {len(results['category_performance'])}")
    print(f"   Improvement Areas Identified: {len(recommendations)}")
    print("\n🎯 Ready for next iteration of improvements!")
    print("=" * 70)

if __name__ == "__main__":
    main()
