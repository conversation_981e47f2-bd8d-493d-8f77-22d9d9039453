"""
Comprehensive Bias Detection System
Combines overt bias detection with subtle/implicit bias detection
Optimized for real courtroom scenarios where subtle biases are more common
"""

from courtroom_bias_detector import HybridIndianCourtroomBiasDetector, BiasDetection
from subtle_bias_detector import SubtleBiasDetector, SubtleBiasDetection, detect_subtle_bias_in_conversation
from typing import Dict, List, Union
import numpy as np
from dataclasses import dataclass
import json

@dataclass
class ComprehensiveBiasReport:
    overt_bias_count: int
    subtle_bias_count: int
    total_bias_instances: int
    risk_level: str
    confidence_distribution: Dict[str, int]
    speaker_risk_assessment: Dict[str, str]
    recommendations: List[str]
    detailed_findings: Dict

class ComprehensiveBiasDetector:
    def __init__(self, enable_ml_models=True):
        """Initialize both overt and subtle bias detectors"""
        self.overt_detector = HybridIndianCourtroomBiasDetector(enable_ml_models=enable_ml_models)
        self.subtle_detector = SubtleBiasDetector()
        
        # Thresholds for real courtroom scenarios
        self.risk_thresholds = {
            'low': {'total_instances': 2, 'high_confidence': 0},
            'medium': {'total_instances': 4, 'high_confidence': 1},
            'high': {'total_instances': 6, 'high_confidence': 2}
        }

    def comprehensive_bias_analysis(self, diarized_segments: List[Dict], 
                                  context_window: int = 5) -> ComprehensiveBiasReport:
        """
        Perform comprehensive bias analysis combining overt and subtle detection
        """
        
        # Step 1: Detect overt biases
        overt_results = self.overt_detector.detect_bias_in_diarized_text(diarized_segments)
        overt_detections = overt_results['detections']
        
        # Step 2: Detect subtle biases
        subtle_results = detect_subtle_bias_in_conversation(diarized_segments, context_window)
        subtle_detections = subtle_results['subtle_detections']
        
        # Step 3: Combine and analyze results
        combined_analysis = self._combine_bias_analyses(overt_detections, subtle_detections)
        
        # Step 4: Generate comprehensive report
        report = self._generate_comprehensive_report(
            overt_detections, subtle_detections, combined_analysis, diarized_segments
        )
        
        return report

    def _combine_bias_analyses(self, overt_detections: List[BiasDetection], 
                              subtle_detections: List[SubtleBiasDetection]) -> Dict:
        """Combine overt and subtle bias analyses"""
        
        # Speaker-wise analysis
        speaker_analysis = {}
        
        # Process overt detections
        for detection in overt_detections:
            speaker = detection.speaker or 'Unknown'
            if speaker not in speaker_analysis:
                speaker_analysis[speaker] = {
                    'overt_count': 0, 'subtle_count': 0, 'total_confidence': 0,
                    'bias_types': set(), 'risk_indicators': []
                }
            
            speaker_analysis[speaker]['overt_count'] += 1
            speaker_analysis[speaker]['total_confidence'] += detection.confidence_score
            speaker_analysis[speaker]['bias_types'].add(detection.bias_type)
            
            if detection.confidence_score > 0.7:
                speaker_analysis[speaker]['risk_indicators'].append('high_confidence_overt')
        
        # Process subtle detections
        for detection in subtle_detections:
            speaker = detection.speaker or 'Unknown'
            if speaker not in speaker_analysis:
                speaker_analysis[speaker] = {
                    'overt_count': 0, 'subtle_count': 0, 'total_confidence': 0,
                    'bias_types': set(), 'risk_indicators': []
                }
            
            speaker_analysis[speaker]['subtle_count'] += 1
            speaker_analysis[speaker]['total_confidence'] += detection.confidence_score
            speaker_analysis[speaker]['bias_types'].add(detection.bias_type)
            
            if detection.subtlety_level == 'microaggression':
                speaker_analysis[speaker]['risk_indicators'].append('microaggression')
            elif detection.confidence_score > 0.6:
                speaker_analysis[speaker]['risk_indicators'].append('high_confidence_subtle')
        
        # Calculate speaker risk levels
        for speaker, data in speaker_analysis.items():
            total_instances = data['overt_count'] + data['subtle_count']
            avg_confidence = data['total_confidence'] / max(1, total_instances)
            
            risk_level = 'low'
            if total_instances >= 4 or len(data['risk_indicators']) >= 2:
                risk_level = 'high'
            elif total_instances >= 2 or len(data['risk_indicators']) >= 1:
                risk_level = 'medium'
            
            data['risk_level'] = risk_level
            data['avg_confidence'] = avg_confidence
        
        return {
            'speaker_analysis': speaker_analysis,
            'total_overt': len(overt_detections),
            'total_subtle': len(subtle_detections),
            'total_instances': len(overt_detections) + len(subtle_detections)
        }

    def _generate_comprehensive_report(self, overt_detections: List[BiasDetection],
                                     subtle_detections: List[SubtleBiasDetection],
                                     combined_analysis: Dict,
                                     diarized_segments: List[Dict]) -> ComprehensiveBiasReport:
        """Generate comprehensive bias report"""
        
        total_instances = combined_analysis['total_instances']
        speaker_analysis = combined_analysis['speaker_analysis']
        
        # Calculate overall risk level
        high_risk_speakers = sum(1 for data in speaker_analysis.values() if data['risk_level'] == 'high')
        medium_risk_speakers = sum(1 for data in speaker_analysis.values() if data['risk_level'] == 'medium')
        
        overall_risk = 'low'
        if high_risk_speakers > 0 or total_instances > 6:
            overall_risk = 'high'
        elif medium_risk_speakers > 0 or total_instances > 3:
            overall_risk = 'medium'
        
        # Confidence distribution
        all_confidences = ([d.confidence_score for d in overt_detections] + 
                          [d.confidence_score for d in subtle_detections])
        
        confidence_dist = {
            'high': sum(1 for c in all_confidences if c > 0.7),
            'medium': sum(1 for c in all_confidences if 0.4 < c <= 0.7),
            'low': sum(1 for c in all_confidences if c <= 0.4)
        }
        
        # Speaker risk assessment
        speaker_risk = {speaker: data['risk_level'] for speaker, data in speaker_analysis.items()}
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            overall_risk, total_instances, speaker_analysis, overt_detections, subtle_detections
        )
        
        # Detailed findings
        detailed_findings = {
            'overt_bias_breakdown': self._analyze_overt_bias(overt_detections),
            'subtle_bias_breakdown': self._analyze_subtle_bias(subtle_detections),
            'speaker_patterns': self._analyze_speaker_patterns(speaker_analysis),
            'temporal_analysis': self._analyze_temporal_patterns(overt_detections, subtle_detections),
            'bias_severity_analysis': self._analyze_bias_severity(overt_detections, subtle_detections)
        }
        
        return ComprehensiveBiasReport(
            overt_bias_count=len(overt_detections),
            subtle_bias_count=len(subtle_detections),
            total_bias_instances=total_instances,
            risk_level=overall_risk,
            confidence_distribution=confidence_dist,
            speaker_risk_assessment=speaker_risk,
            recommendations=recommendations,
            detailed_findings=detailed_findings
        )

    def _generate_recommendations(self, risk_level: str, total_instances: int,
                                speaker_analysis: Dict, overt_detections: List,
                                subtle_detections: List) -> List[str]:
        """Generate actionable recommendations"""
        
        recommendations = []
        
        # Risk-based recommendations
        if risk_level == 'high':
            recommendations.extend([
                "🚨 IMMEDIATE ACTION REQUIRED: High bias risk detected",
                "📋 Review proceedings for potential legal challenges",
                "👥 Consider bias training for court personnel",
                "⚖️ Implement real-time bias monitoring protocols"
            ])
        elif risk_level == 'medium':
            recommendations.extend([
                "⚠️ MONITOR CLOSELY: Moderate bias patterns detected",
                "📊 Track speaker patterns for trend analysis",
                "🔍 Increase scrutiny of future proceedings",
                "📚 Consider refresher training on bias awareness"
            ])
        else:
            recommendations.extend([
                "✅ CONTINUE MONITORING: Low bias risk",
                "📈 Document findings for baseline establishment",
                "🔄 Maintain current bias detection protocols"
            ])
        
        # Specific pattern-based recommendations
        if len(subtle_detections) > len(overt_detections) * 2:
            recommendations.append("🕵️ Focus on subtle bias training - implicit biases predominant")
        
        if any(d.subtlety_level == 'microaggression' for d in subtle_detections):
            recommendations.append("🎯 Address microaggressions through awareness training")
        
        high_risk_speakers = [s for s, data in speaker_analysis.items() if data['risk_level'] == 'high']
        if high_risk_speakers:
            recommendations.append(f"👤 Individual consultation recommended for: {', '.join(high_risk_speakers)}")
        
        return recommendations

    def _analyze_overt_bias(self, detections: List[BiasDetection]) -> Dict:
        """Analyze overt bias patterns"""
        if not detections:
            return {'count': 0, 'types': {}, 'severity': {}}
        
        bias_types = {}
        severity_dist = {}
        
        for detection in detections:
            bias_types[detection.bias_type] = bias_types.get(detection.bias_type, 0) + 1
            severity_dist[detection.severity] = severity_dist.get(detection.severity, 0) + 1
        
        return {
            'count': len(detections),
            'types': bias_types,
            'severity': severity_dist,
            'avg_confidence': np.mean([d.confidence_score for d in detections])
        }

    def _analyze_subtle_bias(self, detections: List[SubtleBiasDetection]) -> Dict:
        """Analyze subtle bias patterns"""
        if not detections:
            return {'count': 0, 'types': {}, 'subtlety': {}}
        
        bias_types = {}
        subtlety_dist = {}
        
        for detection in detections:
            bias_types[detection.bias_type] = bias_types.get(detection.bias_type, 0) + 1
            subtlety_dist[detection.subtlety_level] = subtlety_dist.get(detection.subtlety_level, 0) + 1
        
        return {
            'count': len(detections),
            'types': bias_types,
            'subtlety': subtlety_dist,
            'avg_confidence': np.mean([d.confidence_score for d in detections])
        }

    def _analyze_speaker_patterns(self, speaker_analysis: Dict) -> Dict:
        """Analyze speaker-specific patterns"""
        patterns = {}
        
        for speaker, data in speaker_analysis.items():
            patterns[speaker] = {
                'total_instances': data['overt_count'] + data['subtle_count'],
                'overt_vs_subtle_ratio': data['overt_count'] / max(1, data['subtle_count']),
                'bias_diversity': len(data['bias_types']),
                'risk_indicators': len(data['risk_indicators']),
                'avg_confidence': data['avg_confidence']
            }
        
        return patterns

    def _analyze_temporal_patterns(self, overt_detections: List, subtle_detections: List) -> Dict:
        """Analyze temporal patterns in bias occurrence"""
        # Simplified temporal analysis
        all_detections = [(d.timestamp, 'overt') for d in overt_detections if d.timestamp] + \
                        [(d.timestamp, 'subtle') for d in subtle_detections if d.timestamp]
        
        return {
            'total_timestamped': len(all_detections),
            'temporal_distribution': 'Analysis available with timestamp data'
        }

    def _analyze_bias_severity(self, overt_detections: List, subtle_detections: List) -> Dict:
        """Analyze overall bias severity"""
        
        # Calculate severity score
        severity_score = 0
        
        for detection in overt_detections:
            if detection.severity == 'critical':
                severity_score += 4
            elif detection.severity == 'high':
                severity_score += 3
            elif detection.severity == 'medium':
                severity_score += 2
            else:
                severity_score += 1
        
        for detection in subtle_detections:
            if detection.subtlety_level == 'microaggression':
                severity_score += 2
            else:
                severity_score += 1
        
        total_detections = len(overt_detections) + len(subtle_detections)
        avg_severity = severity_score / max(1, total_detections)
        
        return {
            'severity_score': severity_score,
            'average_severity': avg_severity,
            'severity_level': 'high' if avg_severity > 2.5 else 'medium' if avg_severity > 1.5 else 'low'
        }

    def generate_detailed_report(self, report: ComprehensiveBiasReport) -> str:
        """Generate human-readable detailed report"""
        
        lines = []
        lines.append("🔍 COMPREHENSIVE BIAS ANALYSIS REPORT")
        lines.append("=" * 60)
        
        # Executive Summary
        lines.append(f"\n📊 EXECUTIVE SUMMARY")
        lines.append(f"   Overall Risk Level: {report.risk_level.upper()}")
        lines.append(f"   Total Bias Instances: {report.total_bias_instances}")
        lines.append(f"   Overt Biases: {report.overt_bias_count}")
        lines.append(f"   Subtle Biases: {report.subtle_bias_count}")
        
        # Risk Assessment
        lines.append(f"\n⚠️ RISK ASSESSMENT")
        high_risk_speakers = [s for s, r in report.speaker_risk_assessment.items() if r == 'high']
        medium_risk_speakers = [s for s, r in report.speaker_risk_assessment.items() if r == 'medium']
        
        if high_risk_speakers:
            lines.append(f"   High Risk Speakers: {', '.join(high_risk_speakers)}")
        if medium_risk_speakers:
            lines.append(f"   Medium Risk Speakers: {', '.join(medium_risk_speakers)}")
        
        # Confidence Distribution
        lines.append(f"\n📈 CONFIDENCE DISTRIBUTION")
        for level, count in report.confidence_distribution.items():
            lines.append(f"   {level.title()}: {count} instances")
        
        # Recommendations
        lines.append(f"\n💡 RECOMMENDATIONS")
        for rec in report.recommendations:
            lines.append(f"   {rec}")
        
        # Detailed Findings
        lines.append(f"\n🔍 DETAILED FINDINGS")
        
        overt = report.detailed_findings['overt_bias_breakdown']
        lines.append(f"   Overt Bias Types: {', '.join(overt.get('types', {}).keys())}")
        
        subtle = report.detailed_findings['subtle_bias_breakdown']
        lines.append(f"   Subtle Bias Types: {', '.join(subtle.get('types', {}).keys())}")
        
        severity = report.detailed_findings['bias_severity_analysis']
        lines.append(f"   Overall Severity: {severity['severity_level']}")
        
        return "\n".join(lines)

# Main integration function
def comprehensive_bias_detection(diarized_segments: List[Dict], enable_ml: bool = True) -> Dict:
    """
    Main function for comprehensive bias detection
    Optimized for real courtroom scenarios with focus on subtle biases
    """
    
    detector = ComprehensiveBiasDetector(enable_ml_models=enable_ml)
    report = detector.comprehensive_bias_analysis(diarized_segments)
    detailed_report = detector.generate_detailed_report(report)
    
    return {
        'comprehensive_report': report,
        'detailed_report': detailed_report,
        'summary': {
            'total_bias_instances': report.total_bias_instances,
            'risk_level': report.risk_level,
            'overt_count': report.overt_bias_count,
            'subtle_count': report.subtle_bias_count,
            'high_risk_speakers': [s for s, r in report.speaker_risk_assessment.items() if r == 'high']
        }
    }
