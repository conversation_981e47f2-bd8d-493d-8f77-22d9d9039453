"""
Integration script showing how to add bias detection to your existing pipeline
Audio -> ASR -> Diarization -> Summarization + Bias Detection
"""

from courtroom_bias_detector import IndianCourtroomBiasDetector, integrate_bias_detection
import json
from typing import Dict, List

class CourtroomPipeline:
    def __init__(self):
        self.bias_detector = IndianCourtroomBiasDetector()
        # Initialize your existing components here
        # self.asr_model = ...
        # self.diarization_model = ...
        # self.summarization_model = ...
    
    def process_audio_file(self, audio_file_path: str) -> Dict:
        """
        Complete pipeline processing
        """
        results = {}
        
        # Step 1: Audio to Text (ASR) - Your existing code
        print("Step 1: Converting audio to text...")
        # transcribed_text = self.asr_model.transcribe(audio_file_path)
        # For demo purposes, using placeholder
        transcribed_text = "This is placeholder transcribed text from your ASR model"
        results['transcribed_text'] = transcribed_text
        
        # Step 2: Diarization - Your existing code
        print("Step 2: Performing speaker diarization...")
        # diarized_segments = self.diarization_model.diarize(transcribed_text)
        # For demo purposes, using placeholder
        diarized_segments = [
            {
                'speaker': 'Judge',
                'text': 'The court will now hear the case. The defendant appears to be from a poor family.',
                'timestamp': '00:01:30'
            },
            {
                'speaker': 'Lawyer_Defense',
                'text': 'Your honor, my client is innocent. These accusations are baseless.',
                'timestamp': '00:02:15'
            },
            {
                'speaker': 'Lawyer_Prosecution',
                'text': 'The evidence clearly shows the defendant\'s guilt. People from his community often engage in such activities.',
                'timestamp': '00:03:00'
            }
        ]
        results['diarized_segments'] = diarized_segments
        
        # Step 3: Summarization - Your existing code
        print("Step 3: Generating summary...")
        # summary = self.summarization_model.summarize(diarized_segments)
        # For demo purposes, using placeholder
        summary = "Case summary: Defendant accused of theft. Defense claims innocence. Prosecution presents evidence."
        results['summary'] = summary
        
        # Step 4: NEW - Bias Detection
        print("Step 4: Detecting bias...")
        bias_analysis = self.bias_detector.detect_bias_in_diarized_text(diarized_segments)
        bias_report = self.bias_detector.generate_bias_report(bias_analysis)
        
        results['bias_analysis'] = bias_analysis
        results['bias_report'] = bias_report
        
        return results
    
    def save_results(self, results: Dict, output_file: str):
        """Save all results to a JSON file"""
        # Convert BiasDetection objects to dictionaries for JSON serialization
        serializable_results = self._make_serializable(results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    def _make_serializable(self, obj):
        """Convert custom objects to JSON-serializable format"""
        if hasattr(obj, '__dict__'):
            return {key: self._make_serializable(value) for key, value in obj.__dict__.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        else:
            return obj

# Simple usage functions for quick integration

def quick_bias_check(text: str, speaker: str = "Unknown") -> str:
    """
    Quick function to check bias in a single text segment
    """
    detector = IndianCourtroomBiasDetector()
    detections = detector.detect_bias_in_text(text, speaker)
    
    if not detections:
        return f"No bias detected in {speaker}'s statement."
    
    report = [f"Bias detected in {speaker}'s statement:"]
    for detection in detections:
        report.append(f"- {detection.bias_type}: {detection.severity} severity")
        for instance in detection.instances[:2]:  # Show first 2 instances
            report.append(f"  '{instance}'")
    
    return "\n".join(report)

def add_bias_to_existing_pipeline(your_diarized_output: List[Dict]) -> Dict:
    """
    Drop-in function to add bias detection to your existing pipeline
    Just call this function with your diarized text output
    """
    return integrate_bias_detection(your_diarized_output)

# Example usage and testing
if __name__ == "__main__":
    # Option 1: Full pipeline (replace with your actual implementation)
    print("=== FULL PIPELINE DEMO ===")
    pipeline = CourtroomPipeline()
    results = pipeline.process_audio_file("sample_audio.wav")  # Replace with actual file
    
    print("\n" + "="*50)
    print("BIAS DETECTION REPORT:")
    print("="*50)
    print(results['bias_report'])
    
    # Save results
    pipeline.save_results(results, "courtroom_analysis_results.json")
    print(f"\nResults saved to: courtroom_analysis_results.json")
    
    print("\n" + "="*50)
    print("QUICK BIAS CHECK EXAMPLES:")
    print("="*50)
    
    # Option 2: Quick bias checks
    test_statements = [
        ("Judge", "The defendant, being from a lower caste, is naturally inclined towards crime."),
        ("Lawyer", "Women are too emotional to be reliable witnesses in such serious matters."),
        ("Witness", "I saw the accused person clearly. He seemed like a decent man."),
    ]
    
    for speaker, statement in test_statements:
        print(f"\nTesting: {speaker}")
        print(f"Statement: '{statement}'")
        print(quick_bias_check(statement, speaker))
        print("-" * 40)
