"""
Enhanced Integration script showing how to add hybrid bias detection to your existing pipeline
Audio -> ASR -> Diarization -> Summarization + Advanced Bias Detection
"""

from courtroom_bias_detector import HybridIndianCourtroomBiasDetector, integrate_bias_detection, quick_bias_check_hybrid
import json
import numpy as np
from typing import Dict, List

class EnhancedCourtroomPipeline:
    def __init__(self, enable_ml_bias_detection=True):
        self.bias_detector = HybridIndianCourtroomBiasDetector(enable_ml_models=enable_ml_bias_detection)
        self.enable_ml_bias = enable_ml_bias_detection
        # Initialize your existing components here
        # self.asr_model = ...
        # self.diarization_model = ...
        # self.summarization_model = ...
    
    def process_audio_file(self, audio_file_path: str) -> Dict:
        """
        Complete pipeline processing
        """
        results = {}
        
        # Step 1: Audio to Text (ASR) - Your existing code
        print("Step 1: Converting audio to text...")
        # transcribed_text = self.asr_model.transcribe(audio_file_path)
        # For demo purposes, using placeholder
        transcribed_text = "This is placeholder transcribed text from your ASR model"
        results['transcribed_text'] = transcribed_text
        
        # Step 2: Diarization - Your existing code
        print("Step 2: Performing speaker diarization...")
        # diarized_segments = self.diarization_model.diarize(transcribed_text)
        # For demo purposes, using placeholder
        diarized_segments = [
            {
                'speaker': 'Judge',
                'text': 'The court will now hear the case. The defendant appears to be from a poor family.',
                'timestamp': '00:01:30'
            },
            {
                'speaker': 'Lawyer_Defense',
                'text': 'Your honor, my client is innocent. These accusations are baseless.',
                'timestamp': '00:02:15'
            },
            {
                'speaker': 'Lawyer_Prosecution',
                'text': 'The evidence clearly shows the defendant\'s guilt. People from his community often engage in such activities.',
                'timestamp': '00:03:00'
            }
        ]
        results['diarized_segments'] = diarized_segments
        
        # Step 3: Summarization - Your existing code
        print("Step 3: Generating summary...")
        # summary = self.summarization_model.summarize(diarized_segments)
        # For demo purposes, using placeholder
        summary = "Case summary: Defendant accused of theft. Defense claims innocence. Prosecution presents evidence."
        results['summary'] = summary
        
        # Step 4: NEW - Enhanced Hybrid Bias Detection
        print("Step 4: Detecting bias using hybrid approach...")
        bias_analysis = self.bias_detector.detect_bias_in_diarized_text(diarized_segments)
        bias_report = self.bias_detector.generate_bias_report(bias_analysis)
        detection_summary = self.bias_detector.get_detection_summary(bias_analysis)

        results['bias_analysis'] = bias_analysis
        results['bias_report'] = bias_report
        results['detection_summary'] = detection_summary
        results['bias_confidence_metrics'] = bias_analysis.get('confidence_metrics', {})
        
        return results
    
    def save_results(self, results: Dict, output_file: str):
        """Save all results to a JSON file"""
        # Convert BiasDetection objects to dictionaries for JSON serialization
        serializable_results = self._make_serializable(results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    def _make_serializable(self, obj):
        """Convert custom objects to JSON-serializable format"""
        if hasattr(obj, '__dict__'):
            return {key: self._make_serializable(value) for key, value in obj.__dict__.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        else:
            return obj

# Simple usage functions for quick integration

def enhanced_quick_bias_check(text: str, speaker: str = "Unknown", enable_ml: bool = True) -> str:
    """
    Enhanced quick function to check bias in a single text segment using hybrid approach
    """
    result = quick_bias_check_hybrid(text, speaker, enable_ml)

    if not result['bias_detected']:
        return f"✓ No bias detected in {speaker}'s statement."

    report = [f"✗ Bias detected in {speaker}'s statement:"]
    for bias_type in result['bias_types']:
        report.append(f"  - {bias_type.replace('_', ' ').title()}")

    report.append(f"  - Overall Confidence: {result['overall_confidence']:.2f}")
    report.append(f"  - Detection Methods: {', '.join(result['detection_methods'])}")

    return "\n".join(report)

def add_bias_to_existing_pipeline(your_diarized_output: List[Dict]) -> Dict:
    """
    Drop-in function to add bias detection to your existing pipeline
    Just call this function with your diarized text output
    """
    return integrate_bias_detection(your_diarized_output)

# Example usage and testing
if __name__ == "__main__":
    # Option 1: Enhanced Full pipeline (replace with your actual implementation)
    print("=== ENHANCED PIPELINE DEMO ===")
    pipeline = EnhancedCourtroomPipeline(enable_ml_bias_detection=True)
    results = pipeline.process_audio_file("sample_audio.wav")  # Replace with actual file

    print("\n" + "="*50)
    print("HYBRID BIAS DETECTION REPORT:")
    print("="*50)
    print(results['bias_report'])

    print("\n" + "="*30)
    print("DETECTION SUMMARY:")
    print("="*30)
    summary = results['detection_summary']
    print(f"Summary: {summary['summary']}")
    print(f"Average Confidence: {summary['average_confidence']:.2f}")
    if summary.get('high_risk_speakers'):
        print(f"High Risk Speakers: {', '.join(summary['high_risk_speakers'])}")

    # Save results
    pipeline.save_results(results, "enhanced_courtroom_analysis_results.json")
    print(f"\nResults saved to: enhanced_courtroom_analysis_results.json")

    print("\n" + "="*50)
    print("ENHANCED QUICK BIAS CHECK EXAMPLES:")
    print("="*50)

    # Option 2: Enhanced quick bias checks
    test_statements = [
        ("Judge", "The defendant, being from a lower caste, is naturally inclined towards crime."),
        ("Lawyer", "Women are too emotional to be reliable witnesses in such serious matters."),
        ("Prosecutor", "All Muslims are potential terrorists and cannot be trusted."),
        ("Witness", "I saw the accused person clearly. He seemed like a decent man."),
    ]

    for speaker, statement in test_statements:
        print(f"\nTesting: {speaker}")
        print(f"Statement: '{statement}'")
        print(enhanced_quick_bias_check(statement, speaker, enable_ml=False))
        print("-" * 40)
