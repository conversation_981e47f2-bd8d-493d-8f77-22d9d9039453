# Hybrid Indian Courtroom Bias Detection System

A comprehensive bias detection system specifically designed for Indian courtroom proceedings that combines multiple detection approaches for maximum accuracy.

## 🎯 **Hybrid Approach Overview**

This system uses **4 complementary detection methods** to maximize bias detection accuracy:

### 1. **Rule-Based Detection** (Always Active)
- Enhanced keyword and phrase matching with confidence weights
- Indian context-specific terms (Hindi words, caste terms, regional identifiers)
- Confidence scoring for each match

### 2. **ML-Based Detection** (Optional - Requires transformers)
- Sentiment analysis to detect negative sentiment + identity terms
- Emotion analysis for contextual understanding
- Automatic fallback to rule-based if ML unavailable

### 3. **Contextual Pattern Detection** (Always Active)
- Regex patterns for complex bias expressions
- Context-aware sentence extraction
- Linguistic pattern recognition

### 4. **Semantic Similarity Detection** (Always Active)
- TF-IDF vectorization and cosine similarity
- Comparison with known biased statement templates
- Semantic understanding beyond exact matches

## 🚀 **Key Features**

- **Maximum Detection Coverage**: Combines multiple approaches for comprehensive bias detection
- **Indian Context Aware**: Includes Hindi terms, caste system, regional biases
- **Confidence Scoring**: Each detection includes confidence scores (0.0-1.0)
- **Severity Levels**: Critical, High, Medium, Low based on language intensity
- **Speaker Tracking**: Identifies which speakers exhibit bias patterns
- **Method Consensus**: Higher confidence when multiple methods agree
- **Graceful Degradation**: Works with or without ML dependencies

## 📊 **Bias Types Detected**

1. **Gender Bias**: Including Hindi terms (aurat, mard, ladki, etc.)
2. **Caste Bias**: SC/ST, OBC, Dalit, Brahmin references
3. **Religious Bias**: Hindu, Muslim, Christian, communal language
4. **Economic Bias**: Poor/rich distinctions, class-based assumptions
5. **Regional Bias**: North/South Indian, state-based stereotypes
6. **Language Bias**: English vs vernacular, accent-based discrimination

## 🛠 **Installation**

### Basic Installation (Rule-based only)
```bash
pip install numpy pandas scikit-learn nltk spacy
python -m spacy download en_core_web_sm
```

### Full Installation (All features)
```bash
pip install numpy pandas scikit-learn nltk spacy transformers torch
python -m spacy download en_core_web_sm
```

## 💻 **Usage**

### Quick Integration (Simplest)
```python
from courtroom_bias_detector import integrate_bias_detection

# After your diarization step:
bias_results = integrate_bias_detection(your_diarized_output, enable_ml=True)
print(bias_results['bias_report'])
```

### Advanced Usage
```python
from courtroom_bias_detector import HybridIndianCourtroomBiasDetector

# Initialize detector
detector = HybridIndianCourtroomBiasDetector(enable_ml_models=True)

# Detect bias in text
detections = detector.detect_bias_in_text(text, speaker="Judge")

# Process diarized segments
bias_results = detector.detect_bias_in_diarized_text(diarized_segments)
```

### Pipeline Integration
```python
from pipeline_integration import EnhancedCourtroomPipeline

# Initialize enhanced pipeline
pipeline = EnhancedCourtroomPipeline(enable_ml_bias_detection=True)

# Process audio file
results = pipeline.process_audio_file("courtroom_audio.wav")
print(results['bias_report'])
```

## 📈 **Output Example**

```
=== COURTROOM BIAS DETECTION REPORT ===

Overall Bias Score: 8.5/10
Total Bias Instances: 5

--- Bias Types Detected ---
• Gender Bias: 2 instances
• Caste Bias: 2 instances  
• Religious Bias: 1 instances

--- Severity Distribution ---
• Critical: 2 instances
• High: 2 instances
• Medium: 1 instances

--- Speaker-wise Analysis ---
Judge: Total instances: 2, Main bias types: caste_bias, economic_bias
Lawyer_Prosecution: Total instances: 3, Main bias types: gender_bias, religious_bias

--- Detection Methods Used ---
• Rule-based: 3 detections
• Contextual: 2 detections  
• ML-based: 1 detections
• Semantic: 1 detections
```

## 🔧 **Configuration Options**

### Enable/Disable ML Models
```python
# With ML (best accuracy)
detector = HybridIndianCourtroomBiasDetector(enable_ml_models=True)

# Rule-based only (faster, no ML dependencies)
detector = HybridIndianCourtroomBiasDetector(enable_ml_models=False)
```

### Customize Bias Patterns
```python
# Add custom bias keywords
detector.bias_patterns['custom_bias'] = {
    'keywords': {'custom_term': 0.9},
    'phrases': {'custom phrase': 0.8}
}
```

## 📊 **Performance Characteristics**

- **Accuracy**: High (multiple methods provide cross-validation)
- **Speed**: Fast rule-based core + optional ML enhancement
- **Memory**: Low (efficient pattern matching + optional ML models)
- **Dependencies**: Minimal required, optional ML enhancement

## 🔍 **Detection Confidence**

Each detection includes:
- **Confidence Score**: 0.0-1.0 based on detection strength
- **Detection Method**: Which approach(es) found the bias
- **Severity Level**: Critical/High/Medium/Low
- **Linguistic Markers**: Specific terms/patterns that triggered detection

## 🎛 **Integration Points**

1. **After Diarization**: `integrate_bias_detection(diarized_output)`
2. **Real-time**: `detect_bias_in_text(text, speaker)`
3. **Batch Processing**: `detect_bias_in_diarized_text(segments)`
4. **Pipeline Integration**: Use `EnhancedCourtroomPipeline`

## 🚨 **Error Handling**

- Automatic fallback to rule-based if ML models fail
- Graceful handling of missing dependencies
- Comprehensive logging for debugging
- Robust text preprocessing

## 📝 **Testing**

Run the built-in tests:
```bash
python courtroom_bias_detector.py
python pipeline_integration.py
```

## 🤝 **Customization**

The system is designed to be easily customizable:
- Add new bias categories
- Modify confidence thresholds
- Extend detection patterns
- Integrate with existing pipelines

This hybrid approach ensures maximum bias detection coverage while maintaining flexibility and performance for your Indian courtroom AI system.
