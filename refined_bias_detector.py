"""
Refined Bias Detection System - Addressing Specific Issues
Focus: Fix contextual ambiguity detection and reduce false positives
Target: 70%+ contextual accuracy, <10% false positive rate
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Set, Optional
from dataclasses import dataclass
from collections import defaultdict, Counter
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
import warnings
warnings.filterwarnings('ignore')

# Import from advanced system
from advanced_bias_detector import AdvancedBiasDetection, AdvancedBiasDetector

class RefinedBiasDetector(AdvancedBiasDetector):
    def __init__(self):
        super().__init__()
        
        # ENHANCED BIAS DETECTION PATTERNS - Addressing missed cases
        self.enhanced_bias_patterns = {
            'predictive_bias_patterns': [
                r'we can expect (?:this|that|similar|more|the same) (?:type of )?(?:behavior|pattern|response|conduct)',
                r'(?:will|would) (?:likely|probably|certainly) (?:continue|repeat|do|behave)',
                r'based on (?:his|her|their) (?:history|background|record), (?:he|she|they|this|that)',
                r'given (?:his|her|their) (?:past|history|record|background), (?:we|one) can (?:expect|predict|anticipate)',
                r'(?:typical|predictable|expected) (?:behavior|pattern|response) (?:from|for) (?:someone|people) (?:like|with)',
                r'(?:his|her|their) (?:kind|type|sort) (?:always|usually|typically|tend to) (?:do|behave|act)'
            ],
            'othering_patterns': [
                r'people from (?:that|his|her|their) community (?:don\'t|do not|cannot|can\'t) (?:share|understand|follow)',
                r'(?:that|his|her|their) community (?:doesn\'t|does not|don\'t|do not) (?:share|follow|understand)',
                r'(?:they|those people|that group) (?:don\'t|do not|cannot|can\'t) (?:understand|share|follow)',
                r'unlike (?:us|our|normal|regular|typical) (?:people|community|standards|values)',
                r'different from (?:our|normal|regular|typical|standard) (?:community|people|values|standards)',
                r'(?:his|her|their) (?:people|community|group|kind) (?:have|follow|practice) different (?:values|standards|beliefs)'
            ],
            'type_assumption_patterns': [
                r'as expected from (?:someone|people|a person) (?:of|like|from) (?:his|her|their) (?:type|kind|sort|background)',
                r'(?:typical|predictable|expected) (?:for|from|of) (?:someone|people|a person) (?:like|of|from) (?:his|her|their)',
                r'(?:his|her|their) (?:type|kind|sort) (?:of person|of people) (?:usually|typically|always|often)',
                r'people like (?:him|her|them|that|this) (?:usually|typically|always|often|tend to)',
                r'someone of (?:his|her|their) (?:type|kind|sort|background|caliber) (?:would|should|might)',
                r'(?:naturally|obviously|of course) (?:someone|people) (?:like|from|of) (?:his|her|their) (?:background|type)'
            ]
        }
        
        # ENHANCED NEUTRAL CASE FILTERS - Addressing false positives
        self.enhanced_neutral_filters = {
            'factual_business_observations': [
                r'(?:cash-based|informal|seasonal|temporary) (?:transactions|business|work|employment)',
                r'(?:documentation|record|paperwork) (?:keeping|maintenance|requirements|varies)',
                r'(?:business|work|employment) (?:types|models|structures|practices) (?:vary|differ)',
                r'(?:financial|economic|business) (?:practices|models|structures) (?:in|for|across)',
                r'(?:industry|sector|business) (?:standards|practices|requirements|norms)',
                r'(?:typical|common|standard) (?:business|work|employment) (?:practices|procedures|models)'
            ],
            'legitimate_age_considerations': [
                r'(?:memory|cognitive|physical) (?:issues|concerns|considerations|factors) (?:that|which|may)',
                r'(?:age-related|health-related|medical) (?:considerations|factors|issues|concerns)',
                r'(?:elderly|older) (?:witnesses|individuals|people) (?:sometimes|may|might) (?:have|experience)',
                r'(?:testimony|evidence|witness) (?:reliability|accuracy|consistency) (?:may|might|can) (?:be affected|vary)',
                r'(?:medical|health|physical) (?:conditions|factors|issues) (?:affecting|influencing|impacting)',
                r'(?:legitimate|valid|reasonable) (?:concerns|considerations|factors) (?:about|regarding|for)'
            ],
            'professional_observations': [
                r'(?:requires|needs|involves) (?:interpreter|translation|language) (?:services|support|assistance)',
                r'(?:language|communication) (?:barriers|challenges|differences|requirements)',
                r'(?:cultural|religious|personal) (?:practices|requirements|obligations|considerations)',
                r'(?:scheduling|timing|availability) (?:considerations|requirements|constraints|factors)',
                r'(?:work|employment|job) (?:schedules|commitments|obligations|responsibilities)',
                r'(?:family|personal|religious) (?:obligations|responsibilities|commitments|requirements)'
            ],
            'legal_procedural_language': [
                r'(?:court|legal|judicial) (?:procedures|processes|requirements|protocols)',
                r'(?:evidence|testimony|witness) (?:collection|preparation|presentation|evaluation)',
                r'(?:legal|statutory|procedural) (?:requirements|standards|guidelines|protocols)',
                r'(?:case|trial|hearing) (?:preparation|management|procedures|requirements)',
                r'(?:forensic|investigative|legal) (?:analysis|procedures|protocols|standards)',
                r'(?:advance|prior|adequate) (?:notice|preparation|scheduling|arrangement)'
            ]
        }
        
        # CONFIDENCE ADJUSTMENT FACTORS
        self.confidence_adjustments = {
            'high_confidence_boost': 0.3,    # For clear bias patterns
            'neutral_penalty': -0.4,         # For neutral case matches
            'legal_context_penalty': -0.2,   # For legitimate legal context
            'uncertainty_boost': 0.1         # For uncertain cases needing review
        }

    def refined_bias_detection(self, text: str, speaker: str = None, timestamp: str = None,
                              case_context: Dict = None) -> List[AdvancedBiasDetection]:
        """
        Refined bias detection with enhanced pattern matching and filtering
        """
        detections = []
        text_lower = text.lower()
        
        # 1. Enhanced bias pattern detection
        enhanced_detections = self._enhanced_bias_pattern_detection(text, text_lower, speaker, timestamp)
        detections.extend(enhanced_detections)
        
        # 2. Run advanced detection from parent class
        advanced_detections = super().advanced_bias_detection(text, speaker, timestamp, case_context)
        detections.extend(advanced_detections)
        
        # 3. Enhanced neutral case filtering
        filtered_detections = self._enhanced_neutral_filtering(detections, text, text_lower)
        
        # 4. Refined confidence calibration
        final_detections = self._refined_confidence_calibration(filtered_detections, text, text_lower)
        
        # 5. Remove duplicates
        unique_detections = self._remove_duplicates(final_detections)
        
        return unique_detections

    def _enhanced_bias_pattern_detection(self, text: str, text_lower: str, speaker: str, timestamp: str) -> List[AdvancedBiasDetection]:
        """
        Enhanced bias pattern detection for missed cases
        """
        detections = []
        
        for category, patterns in self.enhanced_bias_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    confidence = 0.8  # High confidence for enhanced patterns
                    uncertainty = 0.2
                    
                    # Calculate legal context score
                    legal_context_score = self._analyze_legal_context(text, text_lower)
                    
                    # Adjust confidence based on legal context
                    if legal_context_score > 0.7:
                        confidence *= 0.7  # Reduce confidence in legal context
                    
                    detection = AdvancedBiasDetection(
                        bias_type=f'enhanced_{category}',
                        severity='high' if confidence > 0.7 else 'medium',
                        confidence_score=confidence,
                        detection_method='enhanced_pattern_detection',
                        context_analysis={'pattern': pattern, 'category': category},
                        uncertainty_score=uncertainty,
                        explanation=f"Enhanced detection of {category}: '{pattern}'",
                        legal_context_score=legal_context_score,
                        semantic_confidence=0.8,
                        neutrality_score=0.1,  # Low neutrality for bias patterns
                        speaker=speaker,
                        timestamp=timestamp,
                        evidence_text=text,
                        alternative_interpretations=[
                            "Could be legitimate professional assessment",
                            "Likely unconscious bias expression",
                            "Possible intentional bias statement"
                        ],
                        recommendation=self._generate_enhanced_recommendation(confidence, legal_context_score)
                    )
                    detections.append(detection)
        
        return detections

    def _enhanced_neutral_filtering(self, detections: List[AdvancedBiasDetection], 
                                   text: str, text_lower: str) -> List[AdvancedBiasDetection]:
        """
        Enhanced neutral case filtering to reduce false positives
        """
        filtered_detections = []
        
        for detection in detections:
            is_neutral = False
            neutrality_boost = 0.0
            
            # Check against enhanced neutral filters
            for category, patterns in self.enhanced_neutral_filters.items():
                for pattern in patterns:
                    if re.search(pattern, text_lower):
                        neutrality_boost += 0.3
                        detection.explanation += f" (Neutral indicator: {category})"
                        
                        # If strong neutral indicators, mark as neutral
                        if neutrality_boost > 0.5:
                            is_neutral = True
                            break
                
                if is_neutral:
                    break
            
            # Update neutrality score
            detection.neutrality_score = min(1.0, detection.neutrality_score + neutrality_boost)
            
            # Filter out if high neutrality
            if detection.neutrality_score > 0.7:
                # Don't add to filtered detections (filter out)
                continue
            else:
                # Adjust confidence based on neutrality
                detection.confidence_score *= (1.0 - detection.neutrality_score * 0.5)
                
                # Only keep if confidence still above threshold
                if detection.confidence_score > 0.3:
                    filtered_detections.append(detection)
        
        return filtered_detections

    def _refined_confidence_calibration(self, detections: List[AdvancedBiasDetection], 
                                       text: str, text_lower: str) -> List[AdvancedBiasDetection]:
        """
        Refined confidence calibration with enhanced adjustments
        """
        for detection in detections:
            original_confidence = detection.confidence_score
            
            # Apply confidence adjustments
            if detection.detection_method == 'enhanced_pattern_detection':
                detection.confidence_score += self.confidence_adjustments['high_confidence_boost']
            
            if detection.neutrality_score > 0.5:
                detection.confidence_score += self.confidence_adjustments['neutral_penalty']
            
            if detection.legal_context_score > 0.7:
                detection.confidence_score += self.confidence_adjustments['legal_context_penalty']
            
            # Ensure confidence stays within bounds
            detection.confidence_score = max(0.1, min(0.95, detection.confidence_score))
            
            # Update uncertainty
            detection.uncertainty_score = 1.0 - detection.confidence_score
            
            # Update severity based on final confidence
            if detection.confidence_score > 0.8:
                detection.severity = 'high'
            elif detection.confidence_score > 0.6:
                detection.severity = 'medium'
            else:
                detection.severity = 'low'
            
            # Update recommendation
            detection.recommendation = self._generate_enhanced_recommendation(
                detection.confidence_score, detection.legal_context_score
            )
        
        return detections

    def _generate_enhanced_recommendation(self, confidence: float, legal_context_score: float) -> str:
        """
        Generate enhanced recommendations based on confidence and context
        """
        if confidence > 0.8 and legal_context_score < 0.3:
            return "HIGH PRIORITY: Clear bias detected, immediate review required"
        elif confidence > 0.7 and legal_context_score < 0.5:
            return "HIGH PRIORITY: Likely bias detected, review recommended"
        elif confidence > 0.6:
            return "MEDIUM PRIORITY: Potential bias, human review suggested"
        elif confidence > 0.4 and legal_context_score < 0.6:
            return "MEDIUM PRIORITY: Uncertain bias, monitor and review"
        elif legal_context_score > 0.7:
            return "LOW PRIORITY: Likely legitimate legal language, monitor only"
        else:
            return "MONITOR: Low confidence detection, track for patterns"

    def _remove_duplicates(self, detections: List[AdvancedBiasDetection]) -> List[AdvancedBiasDetection]:
        """
        Remove duplicate detections, keeping highest confidence
        """
        if not detections:
            return []
        
        # Group by bias type
        grouped = {}
        for detection in detections:
            bias_type = detection.bias_type
            if bias_type not in grouped or detection.confidence_score > grouped[bias_type].confidence_score:
                grouped[bias_type] = detection
        
        return list(grouped.values())

# Integration function
def refined_bias_detection(diarized_segments: List[Dict], case_context: Dict = None) -> Dict:
    """
    Refined bias detection with enhanced accuracy and reduced false positives
    """
    detector = RefinedBiasDetector()
    all_detections = []
    
    for segment in diarized_segments:
        speaker = segment.get('speaker', 'Unknown')
        text = segment.get('text', '')
        timestamp = segment.get('timestamp', '')
        
        # Refined detection
        detections = detector.refined_bias_detection(text, speaker, timestamp, case_context)
        all_detections.extend(detections)
    
    return {
        'refined_detections': all_detections,
        'total_detections': len(all_detections),
        'priority_breakdown': _calculate_priority_breakdown(all_detections),
        'confidence_analysis': _calculate_refined_confidence_analysis(all_detections),
        'system_recommendations': _generate_refined_system_recommendations(all_detections)
    }

def _calculate_priority_breakdown(detections: List[AdvancedBiasDetection]) -> Dict:
    """Calculate priority breakdown for refined system"""
    breakdown = {'high': 0, 'medium': 0, 'low': 0, 'monitor': 0}
    
    for detection in detections:
        if 'HIGH PRIORITY' in detection.recommendation:
            breakdown['high'] += 1
        elif 'MEDIUM PRIORITY' in detection.recommendation:
            breakdown['medium'] += 1
        elif 'LOW PRIORITY' in detection.recommendation:
            breakdown['low'] += 1
        else:
            breakdown['monitor'] += 1
    
    return breakdown

def _calculate_refined_confidence_analysis(detections: List[AdvancedBiasDetection]) -> Dict:
    """Calculate refined confidence analysis"""
    if not detections:
        return {
            'average_confidence': 0.0,
            'average_legal_context': 0.0,
            'average_neutrality': 0.0,
            'high_confidence_count': 0,
            'uncertain_count': 0
        }
    
    return {
        'average_confidence': np.mean([d.confidence_score for d in detections]),
        'average_legal_context': np.mean([d.legal_context_score for d in detections]),
        'average_neutrality': np.mean([d.neutrality_score for d in detections]),
        'high_confidence_count': sum(1 for d in detections if d.confidence_score > 0.8),
        'uncertain_count': sum(1 for d in detections if d.uncertainty_score > 0.6),
        'enhanced_detections': sum(1 for d in detections if 'enhanced_' in d.bias_type)
    }

def _generate_refined_system_recommendations(detections: List[AdvancedBiasDetection]) -> List[str]:
    """Generate refined system recommendations"""
    recommendations = []
    
    high_priority = sum(1 for d in detections if 'HIGH PRIORITY' in d.recommendation)
    if high_priority > 0:
        recommendations.append(f"URGENT: {high_priority} high-priority bias detections require immediate attention")
    
    medium_priority = sum(1 for d in detections if 'MEDIUM PRIORITY' in d.recommendation)
    if medium_priority > 0:
        recommendations.append(f"REVIEW: {medium_priority} medium-priority cases need human review")
    
    enhanced_detections = sum(1 for d in detections if 'enhanced_' in d.bias_type)
    if enhanced_detections > 0:
        recommendations.append(f"PATTERN: {enhanced_detections} enhanced pattern detections identified")
    
    if len(detections) == 0:
        recommendations.append("CLEAR: No bias detected - proceedings appear neutral and professional")
    
    return recommendations

if __name__ == "__main__":
    print("🔧 Refined Bias Detection System")
    print("Enhanced pattern matching and neutral case filtering")
